Metadata-Version: 2.4
Name: pathfinding-cpp
Version: 0.1.0
Summary: Multi-Agent Pathfinding with JPS algorithm - C++ Python bindings
Home-page: https://github.com/yourusername/Multi_agent_pathfinding_cpp
Author: Your Name
Author-email: Your Name <<EMAIL>>
Maintainer-email: Your Name <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/yourusername/Multi_agent_pathfinding_cpp
Project-URL: Documentation, https://github.com/yourusername/Multi_agent_pathfinding_cpp/wiki
Project-URL: Repository, https://github.com/yourusername/Multi_agent_pathfinding_cpp
Project-URL: Bug Tracker, https://github.com/yourusername/Multi_agent_pathfinding_cpp/issues
Project-URL: Changelog, https://github.com/yourusername/Multi_agent_pathfinding_cpp/blob/main/CHANGELOG.md
Keywords: pathfinding,jps,jump-point-search,multi-agent,cpp,pybind11
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: C++
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Description-Content-Type: text/markdown
Requires-Dist: pybind11>=2.6.0
Requires-Dist: numpy>=1.19.0
Provides-Extra: dev
Requires-Dist: pytest>=6.0; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Requires-Dist: black; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: pre-commit; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=6.0; extra == "test"
Requires-Dist: pytest-cov; extra == "test"
Requires-Dist: pytest-benchmark; extra == "test"
Provides-Extra: docs
Requires-Dist: sphinx; extra == "docs"
Requires-Dist: sphinx-rtd-theme; extra == "docs"
Requires-Dist: myst-parser; extra == "docs"
Dynamic: author
Dynamic: home-page
Dynamic: requires-python

# Multi-Agent Pathfinding C++ Python Bindings

这个项目提供了多智能体路径规划算法的 C++ 实现和 Python 绑定。主要使用 Jump Point Search (JPS) 算法进行高效的 3D 路径规划。

## 特性

- **高性能 C++ 实现**: 使用 C++17 编写的 JPS 算法
- **Python 友好接口**: 通过 pybind11 提供简洁的 Python API
- **3D 路径规划**: 支持三维空间中的路径搜索
- **多智能体支持**: 内置碰撞检测和时空占用管理
- **灵活配置**: 支持多种参数调整和优化选项

## 安装

### 前置要求

- Python 3.7+
- C++17 兼容的编译器 (GCC 7+, Clang 5+, MSVC 2017+)
- CMake 3.12+ (可选，我们使用 setup.py)

### 从源码安装

```bash
# 克隆仓库
git clone <repository-url>
cd Multi_agent_pathfinding_cpp

# 安装依赖
pip install -r requirements.txt

# 编译和安装 (开发模式)
pip install -e .

# 或者正常安装
pip install .
```

## 快速开始

```python
import pathfinding_cpp

# 创建坐标转换器
converter = pathfinding_cpp.create_grid_converter(
    lat_size=0.0001, lon_size=0.0001, alt_size=1.0,
    min_lat=30.0, min_lon=120.0, min_alt=0.0
)

# 创建 3D 地图
map3d = pathfinding_cpp.Map3D(100, 100, 10, converter)

# 创建占用图
occupancy_map = pathfinding_cpp.OccupancyMap(100, 100, 10, time_buffer=4)

# 创建 JPS 路径规划器
jps = pathfinding_cpp.JPS(
    map_data=map3d,
    occupancy_map_data=occupancy_map,
    takeoff_speed=1.0,
    cruise_speed=2.0,
    landing_speed=1.0,
    max_steps=10000,
    need_smooth=True,
    smoothness=3.0
)

# 执行路径规划
start = (10, 10, 5)  # (x, y, z)
goal = (90, 90, 5)
result = jps.find_path(start, goal, min_height=5, agent_id="AGENT_1", start_time=0)

if result['success']:
    print(f"找到路径，包含 {len(result['path'])} 个点")
    print(f"起点: {result['path'][0]}")
    print(f"终点: {result['path'][-1]}")
else:
    print(f"路径规划失败: {result['error']}")
```

## API 文档

### 主要类

#### `JPS`
Jump Point Search 路径规划算法的主要实现。

```python
jps = pathfinding_cpp.JPS(
    map_data,           # Map3D 实例
    occupancy_map_data, # OccupancyMap 实例
    takeoff_speed,      # 起飞速度
    cruise_speed,       # 巡航速度
    landing_speed,      # 降落速度
    max_steps,          # 最大搜索步数
    need_smooth,        # 是否需要路径平滑
    smoothness,         # 平滑程度
    heuristic_weight=2.0,  # 启发式权重
    jump_step_size=5       # 跳跃步长
)
```

#### `Map3D`
三维地图，用于存储障碍物信息和可通行性检查。

#### `OccupancyMap`
时空占用图，用于多智能体碰撞检测。

#### `GridConverter`
地理坐标和栅格坐标之间的转换器。

### 返回值格式

`find_path` 方法返回一个字典：

```python
{
    'success': bool,           # 是否成功找到路径
    'path': List[Tuple],       # 完整路径点列表 [(x,y,z,t), ...]
    'turn_path': List[Tuple],  # 转弯点列表 (可选)
    'error': str or None       # 错误信息 (如果失败)
}
```

## 测试

```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_jps_bindings.py

# 运行主测试函数
python tests/test_jps_bindings.py
```

## 开发

### 项目结构

```
Multi_agent_pathfinding_cpp/
├── setup.py                    # 构建脚本
├── pyproject.toml              # 项目配置
├── cpp_implementation/         # C++ 源代码
│   ├── include/               # C++ 头文件
│   ├── src/                   # C++ 源文件
│   └── test/                  # C++ 测试
├── python_bindings/           # pybind11 绑定
│   ├── bindings.cpp          # 主绑定文件
│   └── type_converters.hpp   # 类型转换
├── tests/                     # Python 测试
└── src/                       # Python 源代码 (如果有)
```

### 构建选项

编译时可以通过环境变量控制构建选项：

```bash
# 调试模式
export CMAKE_BUILD_TYPE=Debug
pip install -e .

# 发布模式 (默认)
export CMAKE_BUILD_TYPE=Release
pip install -e .
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v0.1.0
- 初始版本
- 基本的 JPS 算法实现
- Python 绑定支持
- 基础测试覆盖
