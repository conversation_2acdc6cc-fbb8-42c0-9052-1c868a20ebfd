/**
 * @file type_converters.hpp
 * @brief Type conversion utilities for pybind11 bindings
 * 
 * 提供 C++ 和 Python 之间的类型转换辅助函数
 */

#pragma once

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <tuple>
#include <vector>
#include <optional>
#include <string>

// 引入 C++ 项目头文件
#include "GridNode3D.h"
#include "common_types.h"

namespace py = pybind11;

namespace type_converters {

/**
 * @brief 将 Python tuple/list 转换为 GridNode3D
 * @param py_tuple Python 元组，格式为 (x, y, z) 或 (x, y, z, t)
 * @return GridNode3D 对象
 */
inline GridNode3D tuple_to_gridnode3d(const py::tuple& py_tuple) {
    if (py_tuple.size() < 3) {
        throw std::invalid_argument("Tuple must have at least 3 elements (x, y, z)");
    }
    
    float x = py_tuple[0].cast<float>();
    float y = py_tuple[1].cast<float>();
    float z = py_tuple[2].cast<float>();
    long long t = 0;
    
    if (py_tuple.size() >= 4) {
        t = py_tuple[3].cast<long long>();
    }
    
    return GridNode3D(x, y, z, t);
}

/**
 * @brief 将 GridNode3D 转换为 Python tuple
 * @param node GridNode3D 对象
 * @return Python 元组，格式为 (x, y, z, t)
 */
inline py::tuple gridnode3d_to_tuple(const GridNode3D& node) {
    return py::make_tuple(node.x, node.y, node.z, node.t);
}

/**
 * @brief 将 Python tuple 转换为 Point3D
 * @param py_tuple Python 元组，格式为 (x, y, z)
 * @return Point3D 对象
 */
inline Point3D tuple_to_point3d(const py::tuple& py_tuple) {
    if (py_tuple.size() != 3) {
        throw std::invalid_argument("Tuple must have exactly 3 elements (x, y, z)");
    }
    
    int x = py_tuple[0].cast<int>();
    int y = py_tuple[1].cast<int>();
    int z = py_tuple[2].cast<int>();
    
    return std::make_tuple(x, y, z);
}

/**
 * @brief 将 Point3D 转换为 Python tuple
 * @param point Point3D 对象
 * @return Python 元组，格式为 (x, y, z)
 */
inline py::tuple point3d_to_tuple(const Point3D& point) {
    return py::make_tuple(std::get<0>(point), std::get<1>(point), std::get<2>(point));
}

/**
 * @brief 将 GridNode3D 向量转换为 Python tuple 列表
 * @param nodes GridNode3D 向量
 * @return Python 列表，每个元素为 (x, y, z, t) 元组
 */
inline py::list gridnode3d_vector_to_list(const std::vector<GridNode3D>& nodes) {
    py::list result;
    for (const auto& node : nodes) {
        result.append(gridnode3d_to_tuple(node));
    }
    return result;
}

/**
 * @brief 将 Python 列表转换为 GridNode3D 向量
 * @param py_list Python 列表，每个元素为 (x, y, z) 或 (x, y, z, t) 元组
 * @return GridNode3D 向量
 */
inline std::vector<GridNode3D> list_to_gridnode3d_vector(const py::list& py_list) {
    std::vector<GridNode3D> result;
    for (const auto& item : py_list) {
        py::tuple tuple_item = item.cast<py::tuple>();
        result.push_back(tuple_to_gridnode3d(tuple_item));
    }
    return result;
}

/**
 * @brief 创建 Python 字典格式的路径规划结果
 * @param complete_path 完整路径（可选）
 * @param turn_path 转弯点路径（可选）
 * @param error_msg 错误信息（可选）
 * @return Python 字典
 */
inline py::dict create_path_result(
    const std::optional<std::vector<GridNode3D>>& complete_path,
    const std::optional<std::vector<GridNode3D>>& turn_path,
    const std::optional<std::string>& error_msg
) {
    py::dict result;
    
    // 设置成功标志
    result["success"] = complete_path.has_value() && !error_msg.has_value();
    
    // 设置路径数据
    if (complete_path.has_value()) {
        result["path"] = gridnode3d_vector_to_list(complete_path.value());
    } else {
        result["path"] = py::none();
    }
    
    if (turn_path.has_value()) {
        result["turn_path"] = gridnode3d_vector_to_list(turn_path.value());
    } else {
        result["turn_path"] = py::none();
    }
    
    // 设置错误信息
    if (error_msg.has_value()) {
        result["error"] = error_msg.value();
    } else {
        result["error"] = py::none();
    }
    
    return result;
}

/**
 * @brief 验证 Python 参数的有效性
 * @param start_tuple 起点元组
 * @param goal_tuple 终点元组
 * @param min_height 最小高度
 * @param agent_id 智能体ID
 * @param start_time 开始时间
 */
inline void validate_find_path_params(
    const py::tuple& start_tuple,
    const py::tuple& goal_tuple,
    int min_height,
    const std::string& agent_id,
    long long start_time
) {
    if (start_tuple.size() < 3) {
        throw std::invalid_argument("Start tuple must have at least 3 elements (x, y, z)");
    }
    
    if (goal_tuple.size() < 3) {
        throw std::invalid_argument("Goal tuple must have at least 3 elements (x, y, z)");
    }
    
    if (min_height < 0) {
        throw std::invalid_argument("Minimum height must be non-negative");
    }
    
    if (agent_id.empty()) {
        throw std::invalid_argument("Agent ID cannot be empty");
    }
    
    if (start_time < 0) {
        throw std::invalid_argument("Start time must be non-negative");
    }
}

} // namespace type_converters
