import json
import os
import json
import os
import random
import uuid
from kafka import KafkaProducer

# from dotenv import load_dotenv # Removed dotenv
from src.core.map.grid_converter import GridConverter
from datetime import datetime, timedelta

from src.config.settings import initialize_settings
from src.config import settings  # Import settings object

# 全局变量用于记录终点i坐标
current_end_j = 0

initialize_settings(
    r"D:\crscu\route_palnning\codes\Multi-agent-pathfinding-3d-LC-redis-v4-20250430\config.json"
)

# KAFKA_SERVERS = settings.settings.kafka.bootstrap_servers

# # 加载环境变量 # Removed dotenv
# # load_dotenv() # Removed dotenv call

# Kafka配置 from settings
KAFKA_SERVERS = settings.settings.kafka.bootstrap_servers
REQUEST_TOPIC = settings.settings.kafka.flighttask_method_topic
KAFKA_NO_FLY_ZONE_AND_FLIGHT_STATUS = (
    settings.settings.kafka.no_fly_zone_and_flight_status
)

current_time = datetime.now() + timedelta(seconds=60)
unique_id = str(uuid.uuid4())

try:
    # 创建生产者
    producer = KafkaProducer(
        bootstrap_servers=KAFKA_SERVERS,
        value_serializer=lambda x: json.dumps(x).encode("utf-8"),
    )
    print(f"连接Kafka:{KAFKA_SERVERS}成功")
except Exception as e:
    print(f"连接Kafka失败: {e}")
    exit(1)


def generate_batch_plans(num_drones=20, shared_id=None, use_fixed_routes=False):
    """生成批量规划请求"""

    plans = []
    # dataset = settings.settings.database.db_name # 不再需要此行来初始化 GridConverter
    # 从全局 settings 获取当前地图配置对象
    current_map_config = settings.settings.map
    # 使用 MapConfig 对象初始化 GridConverter
    converter = GridConverter(map_config=current_map_config)

    random.seed(124)

    # 如果使用固定航线，加载固定航线数据
    fixed_routes = None
    if use_fixed_routes:
        fixed_routes_path = os.path.join(os.path.dirname(__file__), "fixed_routes.json")
        try:
            with open(fixed_routes_path, "r") as f:
                fixed_routes = json.load(f)
        except Exception as e:
            print(f"加载固定航线文件失败: {e}")
            return []

    for i in range(num_drones):
        if use_fixed_routes and fixed_routes:
            plan_id = shared_id if i == 0 and shared_id else str(uuid.uuid4())
            # 使用固定航线，循环使用固定航线数据
            route = fixed_routes[i % len(fixed_routes)]
            start_coord = route["start"]
            end_coord = route["end"]
            # altitude = random.randint(50, 150)
            altitude = 100
            real_altitude = converter.relative_to_geo(0, 0, altitude)["alt"]
        else:
            plan_id = str(uuid.uuid4())
            # 设置起点和终点之间的最小距离阈值（单位：网格单元）
            min_distance_threshold = 500
            min_center_distance_threshold = 400
            # 生成满足距离阈值的随机坐标
            while True:
                # 生成随机起点坐标
                # 起点i在[0, 4000]范围内随机
                start_i = random.randint(0, 4000)
                # 起点j在[0, 4000]范围内随机
                start_j = random.randint(0, 4000)

                # 生成随机终点坐标
                # 终点i在[0, 4000]范围内随机
                end_i = random.randint(0, 4000)
                # 终点j在[0, 4000]范围内随机
                end_j = random.randint(0, 4000)

                # 计算起点和终点之间的欧几里得距离
                distance = ((end_i - start_i) ** 2 + (end_j - start_j) ** 2) ** 0.5

                # 计算起点到中心点的欧几里得距离
                # start_to_center_distance = (
                #     (start_i - center_i) ** 2 + (start_j - center_j) ** 2
                # ) ** 0.5

                # 计算终点到中心点的欧几里得距离
                # end_to_center_distance = (
                #     (end_i - center_i) ** 2 + (end_j - center_j) ** 2
                # ) ** 0.5

                # 检查所有距离约束条件
                if (
                    distance
                    >= min_distance_threshold
                    #         and start_to_center_distance >= min_center_distance_threshold
                    # and end_to_center_distance >= min_center_distance_threshold
                ):
                    break

            # 生成随机高度，范围为[50, 150]
            altitude = random.randint(50, 150)

            # no_fly_zone_coord = converter.relative_to_geo(10, 10, 30)
            # no_fly_zone_coord1 = converter.relative_to_geo(10, 15, 30)

            # no_fly_zone_coord2 = converter.relative_to_geo(500, 10, 30)
            # no_fly_zone_coord3 = converter.relative_to_geo(500, 15, 30)

            # no_fly_zone_coord3 = converter.relative_to_geo(200, 10, 30)

            # 转换为经纬度坐标
            start_coord = converter.relative_to_geo(start_i, start_j, 30)
            end_coord = converter.relative_to_geo(end_i, end_j, 30)
            real_altitude = converter.relative_to_geo(0, 0, altitude)["alt"]

            print(f"无人机{i + 1} 起点: {start_coord} 终点: {end_coord}")

            # start_test = converter.relative_to_geo(10, 10, 20)
            # end_test = converter.relative_to_geo(10, 20, 20)
            # end_test1 = converter.relative_to_geo(20, 20, 20)
            # end_test2 = converter.relative_to_geo(20, 10, 20)

        # 获取当前时间并往后调整10秒
        current_time = datetime.now() + timedelta(seconds=60)
        end_time = current_time + timedelta(hours=1)

        plan = {
            "bid": str(uuid.uuid4()),  # 每次都生成新的bid
            "id": plan_id,
            # "device_sn": "MD2407011PV" + str(i).zfill(3),
            "module": "input",
            "source": "待提交",
            "desc": "风险评估",
            "data": {
                "id": plan_id,
                "flightapplyid": plan_id,
                "flyHeight": real_altitude,
                "flyMode": "自主飞行",
                "flySecure": "",
                "immeProc": "",
                "landingPoint": f"{end_coord['lon']},{end_coord['lat']},{end_coord['alt']}",
                "mission": "巡检",
                "operator": "o-001",
                "operatorId": "孙鹏远",
                "operatorsMode": "视距内飞行",
                "takeOffPoint": f"{start_coord['lon']},{start_coord['lat']},{start_coord['alt']}",
                "device_sn": "MD2407011PV" + str(i).zfill(3),
                # "device_sn": "1581F6Q8D248A00MN" + str(i + 1).zfill(3),
                "gateway_sn": "7CTDM3D00B2792",
                "uavroot": "青岛_自动规划路径 ",
                "uavrootId": "",
                "zoneId": "",
                "zoneName": "",
                "beginTime": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                "commWay": "",
                "endTime": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "task_source": "0",
            },
            "timestamp": int(current_time.timestamp() * 1000),
        }
        plans.append(plan)
    return plans


def send_message(message, topic=REQUEST_TOPIC):
    """发送消息并打印"""
    try:
        producer.send(topic, message)
        producer.flush()
        print(f"\n已发送消息到 {topic}:")
        print(json.dumps(message, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"发送消息失败: {e}")


def send_batch_plans_sequentially(num_drones=1, use_fixed_routes=False):
    """逐个发送批量规划请求"""
    plans = generate_batch_plans(
        num_drones, shared_id=unique_id, use_fixed_routes=use_fixed_routes
    )
    for i, plan in enumerate(plans):
        print(f"\n正在发送第 {i + 1}/{len(plans)} 个规划请求")
        send_message(plan)


# 生成route消息并设置uavrootId
route_message = generate_batch_plans(1, unique_id)[0]
route_message["data"]["uavrootId"] = "91"

# 测试消息
TEST_MESSAGES = {
    "route": route_message,  # 使用自动航线规划格式，并设置特定的uavrootId
    "approval": {
        "bid": str(uuid.uuid4()),
        "id": unique_id,
        "module": "flowrate",
        "source": "流量评估",
        "desc": "风险评估",
        "data": {
            "flow_risk_state": False,
            "takeoff_time": "2025-03-23 12:00:00",
            "landing_time": [],
            "adjustment_count": 1,
            "flow_adjustment_flightapplyid": [],
            "flow_suggestion": "该飞行计划的申请时间早于当前系统后5分钟,该飞行计划不予通过",
        },
        "timestamp": 1742614491,
    },
    "warning": {
        "bid": str(uuid.uuid4()),
        "sn": "MD2407011PV000",
        "flight_id": unique_id,
        "uavroot_id": "17424567928337692",
        "time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
        "data": [
            {
                "alarmNo": 7920,
                "alarmType": "偏航告警",
                "reason": "偏航警告！当前偏差: inf 米",
                "handleType": "系统自动弹性改航",
                "handleResult": "",
                "alarmReason": "一般告警",
            },
            {
                "alarmNo": 7921,
                "alarmType": "无人机冲突告警",
                "reason": "无人机 1581F6Q8D243S00CCPW5 与无人机 1581F6Q8D243S00CCPW5 存在碰撞风险",
                "handleType": "系统自动弹性改航",
                "handleResult": "",
                "alarmReason": "一般告警",
            },
        ],
        "flag": False,
        "posPre": [31.998611823442384, 118.57451132344238, 50],
        "posFinal": [[29.638917664436686, 106.49085916656091, 0], 0],
        "posFlag": "1",
    },
    # "no_fly_zone": {
    #     "bid": str(uuid.uuid4()),
    #     "center": {"lat": 36.15985657877644, "lon": 120.46085493327983},
    #     "radius": 1000,  # 米
    #     "no_fly_zone_name": "南京圆形禁飞区",
    #     "state": True,
    # },
    # "no_fly_zone": {
    #     "bid": str(uuid.uuid4()),
    #     "center": {"lat": 36.13866267211914, "lon": 120.46645467211914},
    #     "radius": 1000,  # 米
    #     "no_fly_zone_name": "南京管制区",
    #     "state": True,
    # },
    # "no_fly_zone": {
    #     "bid": str(uuid.uuid4()),
    #     "center": {"lat": 31.976765738847657, "lon": 118.57451132344238},
    #     "radius": 100,  # 米
    #     "no_fly_zone_name": "南京测试禁飞区碰撞告警",
    #     "state": True,
    # },
    # "no_fly_zone": {
    #     "bid": str(uuid.uuid4()),
    #     "center": {"lat": 32.006525298219074, "lon": 118.5596524393521},
    #     "radius": 2777.161,  # 米
    #     "no_fly_zone_name": "南京测试新算法禁飞区",
    #     "state": False,
    # },
    # "no_fly_zone": {
    #     "no_fly_zone_name": "南京测试",
    #     "state": True,
    #     "coords": [
    #         {"lng": 118.55548465474526, "lat": 32.02161549153537},
    #         {"lng": 118.58962009315358, "lat": 32.01709122421053},
    #         {"lng": 118.57545368897452, "lat": 32.00169126062128},
    #         {"lng": 118.59538244337656, "lat": 31.982782943814897},
    #         {"lng": 118.53848226917046, "lat": 31.9761660022668},
    #         {"lng": 118.55437818600791, "lat": 31.9937144532443},
    #         {"lng": 118.53135381435933, "lat": 32.00861091447842},
    #     ],
    # },
    "no_fly_zone": {
        "no_fly_zone_name": "南京测试1",
        "state": True,
        "coords": [
            {"lng": 118.63136686017168, "lat": 32.082943585293265},
            {"lng": 118.61995514379593, "lat": 32.06318003333153},
            {"lng": 118.62585746217859, "lat": 32.03474544646459},
            {"lng": 118.64230485453378, "lat": 32.0111216161512},
            {"lng": 118.69420872140387, "lat": 32.017595867282814},
            {"lng": 118.67105617683558, "lat": 32.082743654726606},
        ],
    },
    # "no_fly_zone": {
    #     "bid": str(uuid.uuid4()),
    #     "center": {"lat": 31.968611823442384, "lon": 118.57451132344238},
    #     "radius": 100,  # 米
    #     "no_fly_zone_name": "测试禁飞区起飞",
    #     "state": False,
    # },
}

if __name__ == "__main__":
    while True:
        print("\n请选择要发送的消息类型：")
        print("1. 发送固定航线规划消息")
        print("2. 发送自动航线规划消息")
        print("3. 发送审核系统消息")
        print("4. 发送飞行异常消息")
        print("5. 发送禁飞区消息")
        print("0. 退出")

        try:
            choice = input("\n请输入选项（0-5）: ").strip()

            if choice == "1":
                send_message(TEST_MESSAGES["route"])
            elif choice == "2":
                use_fixed = input("\n使用固定航线？(y/n): ").strip().lower() == "y"
                num_drones = int(input("请输入需要规划的无人机数量: ").strip())
                send_batch_plans_sequentially(num_drones, use_fixed)
            elif choice == "3":
                send_message(TEST_MESSAGES["approval"])
            elif choice == "4":
                send_message(
                    TEST_MESSAGES["warning"], topic=KAFKA_NO_FLY_ZONE_AND_FLIGHT_STATUS
                )
            elif choice == "5":
                send_message(
                    TEST_MESSAGES["no_fly_zone"],
                    topic=KAFKA_NO_FLY_ZONE_AND_FLIGHT_STATUS,
                )
            elif choice == "0":
                print("正在关闭连接...")
                producer.close()
                break
            else:
                print("无效的选项，请重试")
        except KeyboardInterrupt:
            print("\n正在关闭连接...")
            producer.close()
            break
        except Exception as e:
            print(f"发生错误: {e}")
