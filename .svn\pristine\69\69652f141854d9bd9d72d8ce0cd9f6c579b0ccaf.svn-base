// OccupancyMap.cpp
#include "OccupancyMap.h"
#include <algorithm> // for std::sort, std::remove_if

// 构造函数
OccupancyMap::OccupancyMap(int height, int width, int depth, int time_buffer)
    : height_(height), width_(width), depth_(depth), time_buffer_(time_buffer) {}

// 私有辅助函数：在 occupancy_ 中添加新的时间区间，并处理合并逻辑
void OccupancyMap::add_or_merge_interval(const Point3D &point, const TimeInterval &new_interval)
{
    std::vector<TimeInterval> &intervals_at_point = occupancy_[point];

    // 检查是否可以与现有区间合并
    bool merged = false;
    for (TimeInterval &existing_interval : intervals_at_point)
    {
        if (existing_interval.can_merge_with(new_interval))
        {
            existing_interval = existing_interval.merged_with(new_interval);
            merged = true;
            // 在一次合并后，可能需要再次检查这个新合并的区间是否能与其他区间合并
            // 为了简化，我们先添加，然后统一排序和合并所有重叠区间
            break;
        }
    }

    if (!merged)
    {
        intervals_at_point.push_back(new_interval);
    }

    // 排序并合并所有重叠的区间
    std::sort(intervals_at_point.begin(), intervals_at_point.end());

    if (intervals_at_point.empty())
    {
        return;
    }

    std::vector<TimeInterval> merged_intervals;
    merged_intervals.push_back(intervals_at_point[0]);

    for (size_t i = 1; i < intervals_at_point.size(); ++i)
    {
        TimeInterval &last_merged = merged_intervals.back();
        const TimeInterval &current_interval = intervals_at_point[i];

        if (last_merged.can_merge_with(current_interval))
        {
            last_merged = last_merged.merged_with(current_interval);
        }
        else
        {
            merged_intervals.push_back(current_interval);
        }
    }
    intervals_at_point = merged_intervals;
}

// 添加一条飞行路径的占用信息
void OccupancyMap::add_path(const std::string &flight_id, const std::vector<GridNode3D> &path)
{
    if (path.empty())
    {
        return;
    }

    for (const auto &node : path)
    {
        Point3D point = {static_cast<int>(node.x), static_cast<int>(node.y), static_cast<int>(node.z)};
        long long t_node = node.t; // GridNode3D.t 是 long long

        int half_buffer_int = time_buffer_ / 2;
        long long start_t = t_node - static_cast<long long>(half_buffer_int);
        long long end_t = t_node + static_cast<long long>(half_buffer_int);
        // 如果 time_buffer_ 是奇数，例如 5，half_buffer_int 是 2。
        // start_t = t_node - 2, end_t = t_node + 2. 区间是 [t-2, t-1, t, t+1, t+2]，长度为5.
        // 如果 time_buffer_ 是偶数，例如 4，half_buffer_int 是 2。
        // start_t = t_node - 2, end_t = t_node + 2. 区间是 [t-2, t-1, t, t+1, t+2]，长度为5.
        // 这似乎与预期不符，如果 time_buffer_ 是窗口大小，那么 end_t 应该是 start_t + time_buffer_ -1
        // 或者，如果 [start, end] 是半开半闭区间 [start, end), end_t = start_t + time_buffer_
        // 按照之前的讨论，[t_node - T_buffer/2, t_node + T_buffer/2]
        // 如果 T_buffer = 4, half = 2, [t-2, t+2] -> 5个点
        // 如果 T_buffer = 5, half = 2, [t-2, t+2] -> 5个点
        // 为了使区间长度精确为 time_buffer_，并且 t_node 尽可能居中：
        // long long start_t = t_node - (time_buffer_ / 2);
        // long long end_t = start_t + time_buffer_ - 1; // 确保区间长度为 time_buffer_

        TimeInterval current_interval(start_t, end_t, flight_id);

        agent_occupancy_[flight_id].insert(point);
        add_or_merge_interval(point, current_interval);
    }
}

// 检查在特定时间点，某个栅格点是否被占用
bool OccupancyMap::check_collision(const Point3D &point, long long timestamp) const
{
    auto it = occupancy_.find(point);
    if (it == occupancy_.end())
    {
        return false; // 该点没有任何占用记录
    }

    const std::vector<TimeInterval> &intervals_at_point = it->second;
    for (const auto &interval : intervals_at_point)
    {
        if (timestamp >= interval.start_time_ && timestamp <= interval.end_time_)
        {
            // 假设时间戳检查是包含端点的
            return true;
        }
    }
    return false;
}

// 移除一个 agent (及其所有相关的占用信息)
void OccupancyMap::remove_agent(const std::string &flight_id)
{
    auto agent_it = agent_occupancy_.find(flight_id);
    if (agent_it == agent_occupancy_.end())
    {
        return; // 该 agent 不存在
    }

    const std::unordered_set<Point3D, Point3DHash> &occupied_points = agent_it->second;
    for (const auto &point : occupied_points)
    {
        auto occupancy_it = occupancy_.find(point);
        if (occupancy_it != occupancy_.end())
        {
            std::vector<TimeInterval> &intervals_at_point = occupancy_it->second;

            // 使用 remove_if 和 erase 来移除所有属于该 flight_id 的时间区间
            intervals_at_point.erase(
                std::remove_if(intervals_at_point.begin(), intervals_at_point.end(),
                               [&](const TimeInterval &interval)
                               {
                                   return interval.flight_id_ == flight_id;
                               }),
                intervals_at_point.end());

            // 如果移除后该点的占用列表为空，则从 occupancy_ 映射中移除该点
            if (intervals_at_point.empty())
            {
                occupancy_.erase(occupancy_it);
            }
        }
    }
    // 从 agent_occupancy_ 中移除该 agent
    agent_occupancy_.erase(agent_it);
}

// (可选的 Getter 函数实现)
std::vector<TimeInterval> OccupancyMap::get_occupancies_at_point(const Point3D &point) const
{
    auto it = occupancy_.find(point);
    if (it != occupancy_.end())
    {
        return it->second;
    }
    return {}; // 返回空向量
}

std::unordered_set<Point3D, Point3DHash> OccupancyMap::get_agent_occupied_points(const std::string &flight_id) const
{
    auto it = agent_occupancy_.find(flight_id);
    if (it != agent_occupancy_.end())
    {
        return it->second;
    }
    return {}; // 返回空的 unordered_set
}
