import numpy as np
from collections import defaultdict
from typing import List, Tuple, Dict, Set, Optional
from ...utils.logging import get_logger
from ...config import settings  # Corrected import

logger = get_logger(__name__)


class TimeInterval:
    def __init__(self, start: int, end: int, agent_id: str):
        self.start = start
        self.end = end
        self.agent_id = agent_id

    def overlaps(self, other: "TimeInterval") -> bool:
        """检查两个时间区间是否重叠"""
        return not (self.end < other.start or other.end < self.start)

    def merge(self, other: "TimeInterval") -> Optional["TimeInterval"]:
        """如果两个区间重叠且属于同一个agent，则合并它们"""
        if self.agent_id != other.agent_id:
            return None
        if self.end + 1 < other.start:  # +1 确保连续的时间点也被合并
            return None
        return TimeInterval(
            min(self.start, other.start), max(self.end, other.end), self.agent_id
        )


class OccupancyMap:
    # 类变量，所有实例共享
    map_size = None
    height = None
    width = None
    depth = None
    # 使用(y,x,z)作为键，值为TimeInterval对象列表的字典
    occupancy: Dict[Tuple[int, int, int], List[TimeInterval]] = defaultdict(list)
    # 时间安全余量
    time_buffer = 0
    # 为每个agent维护一个空间占用集合（包括安全区域）
    agent_occupancy: Dict[str, Set[Tuple[int, int, int]]] = defaultdict(set)

    def __init__(self, map_size, time_buffer=0):
        """
        初始化时空占用图
        :param map_size: (height, width, depth) 地图大小
        :param time_buffer: 时间安全余量（秒），在占用时间点前后各扩展这么多秒
        """
        # 只在第一次初始化时设置类变量
        if OccupancyMap.map_size is None:
            OccupancyMap.map_size = map_size
            OccupancyMap.height, OccupancyMap.width, OccupancyMap.depth = map_size
            OccupancyMap.time_buffer = time_buffer

    def _merge_intervals(self, intervals: List[TimeInterval]) -> List[TimeInterval]:
        """
        合并同一个agent的重叠时间区间
        :param intervals: TimeInterval对象列表
        :return: 合并后的TimeInterval对象列表
        """
        if not intervals:
            return []

        # 按agent_id和开始时间排序
        intervals.sort(key=lambda x: (x.agent_id, x.start))

        merged = []
        current = intervals[0]

        for interval in intervals[1:]:
            if interval.agent_id == current.agent_id:
                merged_interval = current.merge(interval)
                if merged_interval:
                    current = merged_interval
                else:
                    merged.append(current)
                    current = interval
            else:
                merged.append(current)
                current = interval

        merged.append(current)
        return merged

    def add_path(self, path, agent_id):
        """
        添加一条路径到占用图中
        :param path: 路径点列表，每个点包含 (y, x, z, t)
        :param agent_id: 智能体ID
        """
        # 预先计算偏移量和增量
        current_path_safety_offsets = (
            settings.settings.pathplanning.safety_offsets
        )  # Corrected access
        deltas = [-current_path_safety_offsets, 0, current_path_safety_offsets]

        # 临时存储此路径为每个位置生成的新区间
        new_intervals_for_path = defaultdict(list)
        # 跟踪受此路径影响的位置，以便稍后合并
        affected_positions_this_path = set()

        for node in path:
            # 获取当前位置和时间
            pos = (round(node.y), round(node.x), round(node.z))
            t = node.t

            # 添加安全区域占用（空间和时间）
            for dy in deltas:
                for dx in deltas:
                    for dz in deltas:
                        y = pos[0] + dy
                        x = pos[1] + dx
                        z = pos[2] + dz
                        # 检查是否在地图范围内
                        if (
                            0 <= y < OccupancyMap.height
                            and 0 <= x < OccupancyMap.width
                            and 0 <= z < OccupancyMap.depth
                        ):
                            occupancy_pos = (y, x, z)
                            # 添加空间占用
                            OccupancyMap.agent_occupancy[agent_id].add(occupancy_pos)

                            # 创建时间区间
                            interval = TimeInterval(
                                t - OccupancyMap.time_buffer,
                                t + OccupancyMap.time_buffer,
                                agent_id,
                            )
                            # 收集新区间
                            new_intervals_for_path[occupancy_pos].append(interval)
                            affected_positions_this_path.add(occupancy_pos)

        # 处理完所有节点后，对受影响的位置进行区间合并
        for occupancy_pos in affected_positions_this_path:
            # 1. 将当前路径为此位置生成的所有新区间添加到主占用列表
            if new_intervals_for_path[occupancy_pos]:  # 确保有新区间才extend
                OccupancyMap.occupancy[occupancy_pos].extend(
                    new_intervals_for_path[occupancy_pos]
                )

            # 2. 应用原始的合并条件
            if len(OccupancyMap.occupancy[occupancy_pos]) > 10:
                OccupancyMap.occupancy[occupancy_pos] = self._merge_intervals(
                    OccupancyMap.occupancy[occupancy_pos]
                )

    def check_collision(self, pos, t) -> Tuple[bool, Optional[str]]:
        """
        检查某个位置在某个时间点是否有碰撞
        :param pos: (y, x, z) 位置
        :param t: 时间点
        :return: (是否有碰撞, 如果有碰撞则返回占用的agent_id)
        """
        y, x, z = pos
        # 检查是否在地图范围内
        if not (
            0 <= y < OccupancyMap.height
            and 0 <= x < OccupancyMap.width
            and 0 <= z < OccupancyMap.depth
        ):
            return True, None

        # 检查时间点是否在任何占用区间内
        intervals = OccupancyMap.occupancy[(y, x, z)]
        for interval in intervals:
            if interval.start <= t <= interval.end:
                return True, interval.agent_id
        return False, None

    def get_conflict(
        self, pos1, pos2, t
    ) -> Optional[Tuple[int, int, int, int, str, str]]:
        """
        检查两个位置在给定时间点是否冲突
        :param pos1: (y1, x1, z1) 第一个位置
        :param pos2: (y2, x2, z2) 第二个位置
        :param t: 时间点
        :return: 如果有冲突返回 (y1, x1, z1, t, agent1_id, agent2_id)，否则返回None
        """
        y1, x1, z1 = pos1
        y2, x2, z2 = pos2

        # 检查是否在安全区域内
        if abs(y1 - y2) <= 1 and abs(x1 - x2) <= 1 and abs(z1 - z2) <= 1:
            # 检查两个位置的占用情况
            collision1, agent1 = self.check_collision(pos1, t)
            collision2, agent2 = self.check_collision(pos2, t)

            if collision1 and collision2 and agent1 != agent2:
                return (y1, x1, z1, t, agent1, agent2)
        return None

    def get_occupying_agents(self, pos, t) -> Set[str]:
        """
        获取某个位置在某个时间点的所有占用agent
        :param pos: (y, x, z) 位置
        :param t: 时间点
        :return: agent_id集合
        """
        agents = set()
        intervals = OccupancyMap.occupancy[pos]
        for interval in intervals:
            if interval.start <= t <= interval.end:
                agents.add(interval.agent_id)
        return agents

    def clear(self):
        """清空占用图"""
        OccupancyMap.occupancy.clear()
        OccupancyMap.agent_occupancy.clear()

    def remove_agent(self, agent_id: str):
        """
        删除指定agent的所有占用信息
        :param agent_id: 要删除的agent ID
        """
        # 直接获取所有需要清理的位置
        if agent_id in OccupancyMap.agent_occupancy:
            for pos in OccupancyMap.agent_occupancy[agent_id]:
                OccupancyMap.occupancy[pos] = [
                    interval
                    for interval in OccupancyMap.occupancy[pos]
                    if interval.agent_id != agent_id
                ]
                if not OccupancyMap.occupancy[pos]:
                    del OccupancyMap.occupancy[pos]

            # 清除空间占用索引
            del OccupancyMap.agent_occupancy[agent_id]
            # logger.info(f"已从占用图中删除agent {agent_id}")

    def find_valid_time(
        self, path, agent_id, start_time, time_direction=0, max_offset=3600
    ) -> Tuple[Optional[int], bool]:
        """
        寻找可行的起飞时间
        Args:
            path: 路径点列表
            agent_id: 飞行器ID
            start_time: 期望的起飞时间
            time_direction: 时间搜索方向 (1:向后, -1:向前, 0:双向)
            max_offset: 最大时间偏移量（秒）
        Returns:
            Tuple[Optional[int], bool]: (时间, 是否被修改)
            - 如果返回(None, False)表示没找到可行时间
            - 如果返回(time, False)表示原时间可行
            - 如果返回(time, True)表示找到了新的可行时间
        """
        current_time = start_time
        original_time = start_time

        # 记录已尝试过的时间点
        tried_times = set()

        while abs(current_time - original_time) <= max_offset:
            if current_time in tried_times:
                continue

            tried_times.add(current_time)

            # 收集冲突
            conflicts = []
            for node in path:
                relative_t = node.t - start_time
                actual_t = current_time + relative_t

                # 确保时间不为负
                if actual_t < 0:
                    conflicts.append(
                        {
                            "interval": TimeInterval(0, 0, "INVALID_TIME"),
                            "relative_t": relative_t,
                        }
                    )
                    continue

                intervals = OccupancyMap.occupancy[(node.y, node.x, node.z)]
                for interval in intervals:
                    if (
                        interval.agent_id != agent_id
                        and interval.start <= actual_t <= interval.end
                    ):
                        conflicts.append(
                            {"interval": interval, "relative_t": relative_t}
                        )

            if not conflicts:
                return current_time, current_time != start_time

            # 计算时间偏移
            if time_direction >= 0:  # 向后搜索
                forward_offset = max(
                    conflict["interval"].end
                    + 1
                    - (current_time + conflict["relative_t"])
                    for conflict in conflicts
                )

            if time_direction <= 0:  # 向前搜索
                backward_offset = min(
                    conflict["interval"].start
                    - 1
                    - (current_time + conflict["relative_t"])
                    for conflict in conflicts
                )

            # 根据搜索方向更新时间
            if time_direction > 0:
                current_time += forward_offset
            elif time_direction < 0:
                new_time = current_time + backward_offset
                if new_time >= 0:  # 确保时间不为负
                    current_time = new_time
                else:
                    current_time += forward_offset  # 如果向前失败，尝试向后
            else:  # 双向搜索，选择偏移量较小的方向
                if (
                    abs(forward_offset) <= abs(backward_offset)
                    or current_time + backward_offset < 0
                ):
                    current_time += forward_offset
                else:
                    current_time += backward_offset

        return None, False  # 超出最大偏移范围

    def copy(self) -> "OccupancyMap":
        """
        创建占用图的深拷贝
        :return: 新的OccupancyMap实例
        """
        # 由于使用类变量，copy方法将返回同一个共享实例
        return OccupancyMap(OccupancyMap.map_size, OccupancyMap.time_buffer)
