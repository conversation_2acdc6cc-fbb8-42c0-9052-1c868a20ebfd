// 航路占用时空管理器
#pragma once

#include "common_types.h" // 引入基础数据类型
#include "GridNode3D.h"   // 引入三维栅格节点定义
#include <string>         // 用于字符串处理
#include <vector>         // 用于存储路径点
#include <map>            // 用于存储航路信息
#include <unordered_set>  // 用于存储空间占用点集
#include <algorithm>      // 用于排序和移除操作
#include <stdexcept>      // 用于异常处理

class OccupancyMap
{
public:
    // 占用图构造函数
    // @param height: 地图高度（栅格数）
    // @param width: 地图宽度（栅格数）
    // @param depth: 地图深度（栅格数）
    // @param time_buffer: 每个位置的时间缓冲区大小（秒）
    OccupancyMap(int height, int width, int depth, int time_buffer);

    // 添加一条航路的占用信息
    // @param flight_id: 航班ID
    // @param path: 航路路径点序列
    void add_path(const std::string &flight_id, const std::vector<GridNode3D> &path);

    // 检查指定时间点的空间占用情况
    // @param point: 待检查的三维栅格点
    // @param timestamp: 检查时间点
    // @return: 如果该点在指定时间被占用返回true，否则返回false
    bool check_collision(const Point3D &point, long long timestamp) const;

    // 移除指定航班的所有占用信息
    // @param flight_id: 要移除的航班ID
    void remove_agent(const std::string &flight_id);

    // 获取指定点的所有时间占用区间
    // @param point: 待查询的三维栅格点
    // @return: 返回该点所有的时间占用区间列表
    std::vector<TimeInterval> get_occupancies_at_point(const Point3D &point) const;

    // 获取指定航班占用的所有空间点
    // @param flight_id: 航班ID
    // @return: 返回该航班占用的所有栅格点集合
    std::unordered_set<Point3D, Point3DHash> get_agent_occupied_points(const std::string &flight_id) const;

private:
    // 地图尺寸
    int height_;      // 地图高度（栅格数）
    int width_;       // 地图宽度（栅格数）
    int depth_;       // 地图深度（栅格数）
    int time_buffer_; // 时间缓冲区大小（秒），定义每个位置的占用时间窗口

    // 航班空间占用信息
    // 记录每个航班ID占用的所有三维栅格点
    std::map<std::string, std::unordered_set<Point3D, Point3DHash>> agent_occupancy_;

    // 栅格点时间占用信息
    // 键：三维栅格点坐标
    // 值：该点上所有的时间占用区间列表
    std::map<Point3D, std::vector<TimeInterval>> occupancy_;

    // 添加或合并时间区间
    // @param point: 要添加占用信息的栅格点
    // @param new_interval: 新的时间占用区间
    // 说明：该函数会处理时间区间的重叠和合并，确保同一航班在同一位置的连续或重叠时间段被合并
    void add_or_merge_interval(const Point3D &point, const TimeInterval &new_interval);
};
