"""
数据库连接管理器模块
负责异步管理数据库连接的创建、检查、清理和保活
"""

from typing import Optional, Callable, Any
import threading
import time
import random
import mysql.connector
from concurrent.futures import ThreadPoolExecutor
from ..config import settings
from ..utils.logging import get_logger
from ..utils.unified_timer_manager import UnifiedTimerManager

logger = get_logger(__name__)


class DBConnectionManager:
    """数据库连接管理器，负责异步管理数据库连接"""

    # 单例模式
    _instance = None
    _lock = threading.Lock()

    # 数据库连接相关
    db_conn = None  # 数据库连接实例
    db_conn_last_active = None  # 记录最后活动时间

    # 定时器相关 - 使用统一定时器间隔
    timer_interval = 60  # 统一定时器间隔（秒）

    # 统一定时器管理器
    unified_timer_manager = None

    # 线程池
    _executor = None

    # 关闭状态标志
    is_shutting_down = False

    def __new__(cls):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DBConnectionManager, cls).__new__(cls)
                # 初始化线程池 - 减少线程数以降低内存使用
                cls._instance._executor = ThreadPoolExecutor(
                    max_workers=1,  # 进一步减少最大工作线程数，只用于执行数据库操作
                    thread_name_prefix="DBManager",  # 添加线程名称前缀，便于调试
                )
                # 初始化统一定时器管理器
                cls._instance.unified_timer_manager = UnifiedTimerManager(
                    timer_interval=cls.timer_interval
                )
                # 初始化数据库连接
                cls._instance._init_db_conn()
                # 注册定时任务
                cls._instance._register_timer_tasks()
            return cls._instance

    def _init_db_conn(self) -> None:
        """初始化数据库连接"""
        thread_id = threading.get_ident()
        logger.debug(
            f"[Thread-{thread_id}] Attempting to initialize DB connection. Current db_conn is None: {DBConnectionManager.db_conn is None}"
        )

        # 如果已经有连接，先检查连接是否有效
        if DBConnectionManager.db_conn is not None:
            try:
                # 尝试执行简单查询测试连接
                with DBConnectionManager.db_conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                # 连接有效，更新最后活动时间并返回
                DBConnectionManager.db_conn_last_active = time.time()
                logger.debug(
                    f"[Thread-{thread_id}] Existing DB connection (ID: {id(DBConnectionManager.db_conn)}) is valid, skipping initialization."
                )
                return
            except Exception as test_error:
                # 连接无效，记录错误并继续初始化新连接
                logger.warning(
                    f"[Thread-{thread_id}] Existing DB connection (ID: {id(DBConnectionManager.db_conn)}) failed test: {str(test_error)}"
                )
                try:
                    # 尝试关闭失效的连接
                    DBConnectionManager.db_conn.close()
                except Exception:
                    pass  # 忽略关闭错误
                # 设置为None以便重新初始化
                DBConnectionManager.db_conn = None

        # 重试机制
        max_retries = 3
        retry_count = 0
        backoff_time = 1  # 初始等待时间（秒）

        while retry_count < max_retries:
            try:
                # 创建连接配置字典 - 完全禁用连接池
                conn_config = {
                    "host": settings.settings.database.host,
                    "port": settings.settings.database.port,
                    "user": settings.settings.database.user,
                    "password": settings.settings.database.password,
                    "database": settings.settings.database.database,
                    "use_pure": True,
                    "autocommit": True,
                    "buffered": True,
                    "get_warnings": True,
                    "raise_on_warnings": True,
                    "connection_timeout": 30,
                    "connect_timeout": 15,
                    "ssl_disabled": True,
                }

                # 创建数据库连接
                new_conn = mysql.connector.connect(**conn_config)

                # 测试新连接
                with new_conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

                # 连接测试成功，更新全局连接对象
                DBConnectionManager.db_conn = new_conn
                DBConnectionManager.db_conn_last_active = time.time()
                logger.debug(
                    f"[Thread-{thread_id}] DB connection initialized successfully. db_conn object ID: {id(DBConnectionManager.db_conn)}"
                )
                # 成功创建连接，跳出重试循环
                break

            except Exception as e:
                retry_count += 1
                logger.error(
                    f"[Thread-{thread_id}] DB connection initialization failed (attempt {retry_count}/{max_retries}): {str(e)}",
                    exc_info=True,
                )

                # 如果还有重试机会，等待后重试
                if retry_count < max_retries:
                    logger.warning(
                        f"[Thread-{thread_id}] Retrying in {backoff_time} seconds..."
                    )
                    time.sleep(backoff_time)
                    # 指数退避，增加等待时间
                    backoff_time *= 2
                else:
                    # 所有重试都失败，确保连接为None
                    logger.critical(
                        f"[Thread-{thread_id}] All DB connection initialization attempts failed after {max_retries} retries."
                    )
                    DBConnectionManager.db_conn = None

    def _register_timer_tasks(self) -> None:
        """注册所有定时任务到统一定时器管理器"""
        # 注册数据库连接检查任务
        self.unified_timer_manager.register_task("db_conn_check", self._check_db_conn)
        logger.info("已注册数据库连接检查任务")

        # 注册连接池清理任务
        self.unified_timer_manager.register_task(
            "db_pool_cleanup", self._cleanup_db_pool
        )
        logger.info("已注册连接池清理任务")

        # 注册数据库连接保活任务
        self.unified_timer_manager.register_task(
            "db_conn_keepalive", self._keepalive_db_conn
        )
        logger.info("已注册数据库连接保活任务")

    def _cleanup_db_pool(self) -> None:
        """定期清理连接池"""
        cleanup_thread_id = threading.get_ident()
        try:
            logger.debug(f"[Thread-{cleanup_thread_id}] 执行定期连接池清理检查")

            # 检查连接是否为None
            if DBConnectionManager.db_conn is None:
                # 检查是否正在关闭
                if DBConnectionManager.is_shutting_down:
                    logger.debug(
                        f"[Thread-{cleanup_thread_id}] 系统正在关闭，跳过数据库连接初始化"
                    )
                else:
                    logger.warning(
                        f"[Thread-{cleanup_thread_id}] 数据库连接为None，尝试重新初始化"
                    )
                    self._init_db_conn()
        except Exception as e:
            logger.error(
                f"[Thread-{cleanup_thread_id}] 定期连接池清理失败: {str(e)}",
                exc_info=True,
            )
            # 如果清理过程中出错，尝试重新初始化连接
            try:
                if DBConnectionManager.db_conn is not None:
                    try:
                        DBConnectionManager.db_conn.close()
                    except:
                        pass
                DBConnectionManager.db_conn = None
                logger.warning(
                    f"[Thread-{cleanup_thread_id}] 清理失败，强制重置连接并尝试重新初始化"
                )
                self._init_db_conn()
            except Exception as init_error:
                logger.error(
                    f"[Thread-{cleanup_thread_id}] 重新初始化连接失败: {str(init_error)}"
                )

    def _keepalive_db_conn(self) -> None:
        """定期执行连接保活操作"""
        keepalive_thread_id = threading.get_ident()
        try:
            logger.debug(f"[Thread-{keepalive_thread_id}] 执行定期数据库连接保活操作")

            # 执行一个简单的查询来保持连接活跃
            if DBConnectionManager.db_conn is not None:
                with DBConnectionManager.db_conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                logger.debug(f"[Thread-{keepalive_thread_id}] 数据库连接保活操作成功")
                # 更新最后活动时间
                DBConnectionManager.db_conn_last_active = time.time()
            else:
                # 检查是否正在关闭
                if DBConnectionManager.is_shutting_down:
                    logger.debug(
                        f"[Thread-{keepalive_thread_id}] 系统正在关闭，跳过数据库连接初始化"
                    )
                else:
                    logger.debug(
                        f"[Thread-{keepalive_thread_id}] 数据库连接为None，无法执行保活操作"
                    )
                    self._init_db_conn()
        except Exception as e:
            logger.error(
                f"[Thread-{keepalive_thread_id}] 定期数据库连接保活操作失败: {str(e)}"
            )
            # 连接可能已断开，尝试重新初始化
            try:
                if DBConnectionManager.db_conn is not None:
                    try:
                        DBConnectionManager.db_conn.close()
                    except:
                        pass
                DBConnectionManager.db_conn = None
                self._init_db_conn()
                logger.info(
                    f"[Thread-{keepalive_thread_id}] 保活失败后重新初始化连接成功"
                )
            except Exception as init_error:
                logger.error(
                    f"[Thread-{keepalive_thread_id}] 保活失败后重新初始化连接失败: {str(init_error)}"
                )

    # 已移除 _start_db_conn_check_timer 方法，使用统一定时器管理器替代

    # 已移除 _start_db_pool_cleanup_timer 方法，使用统一定时器管理器替代

    # 已移除 _start_db_conn_keepalive_timer 方法，使用统一定时器管理器替代

    def get_connection(self) -> Optional[mysql.connector.connection.MySQLConnection]:
        """获取数据库连接（如果连接不可用，会尝试重新初始化）"""
        if not self._check_db_conn():
            self._init_db_conn()
        return DBConnectionManager.db_conn

    def _check_db_conn(self) -> bool:
        """检查并维护数据库连接"""
        # 获取线程ID，并处理可能为None的情况
        try:
            thread_id = threading.get_ident()
            if thread_id is None:
                logger.warning("当前线程ID为None，可能是线程已终止或未正确初始化")
                # 如果系统正在关闭，直接返回False
                if DBConnectionManager.is_shutting_down:
                    return False
                # 使用一个固定值代替None
                thread_id = "unknown"
        except Exception as e:
            logger.warning(f"获取线程ID时出错: {str(e)}")
            thread_id = "error"

            # 如果系统正在关闭，直接返回False
            if DBConnectionManager.is_shutting_down:
                return False

        if DBConnectionManager.db_conn is None:
            # 检查是否正在关闭
            if DBConnectionManager.is_shutting_down:
                logger.debug(f"[Thread-{thread_id}] 系统正在关闭，跳过数据库连接初始化")
                return False
            else:
                logger.warning(
                    f"[Thread-{thread_id}] DB connection is None, attempting re-initialization."
                )
                try:
                    self._init_db_conn()
                except Exception as init_error:
                    logger.error(
                        f"[Thread-{thread_id}] 初始化数据库连接失败: {str(init_error)}"
                    )
                    return False

                # 检查初始化是否成功
                if DBConnectionManager.db_conn:
                    logger.info(
                        f"[Thread-{thread_id}] Re-initialization successful after check."
                    )
                    return True
                else:
                    logger.error(
                        f"[Thread-{thread_id}] Re-initialization failed after check."
                    )
                    return False

        # 检查连接是否超时或失效
        # 使用本地变量保存当前连接对象，避免并发修改
        try:
            current_conn_obj = DBConnectionManager.db_conn
        except Exception as e:
            logger.error(f"[Thread-{thread_id}] 获取数据库连接对象时出错: {str(e)}")
            DBConnectionManager.db_conn = None
            return False

        # 先检查连接对象是否为None
        if current_conn_obj is None:
            logger.warning(
                f"[Thread-{thread_id}] current_conn_obj is None after assignment. Attempting re-init."
            )
            try:
                self._init_db_conn()  # Attempt to re-initialize
            except Exception as init_error:
                logger.error(
                    f"[Thread-{thread_id}] 重新初始化数据库连接失败: {str(init_error)}"
                )
                return False
            return bool(DBConnectionManager.db_conn)

        try:
            # 尝试获取游标并执行测试查询
            try:
                # 首先检查连接是否仍然有效
                try:
                    # 使用is_connected()方法检查连接状态
                    if (
                        hasattr(current_conn_obj, "is_connected")
                        and not current_conn_obj.is_connected()
                    ):
                        logger.warning(
                            f"[Thread-{thread_id}] Connection is no longer connected. Recreating connection."
                        )
                        # 连接已断开，强制重新初始化
                        DBConnectionManager.db_conn = None
                        self._init_db_conn()
                        return bool(DBConnectionManager.db_conn)
                except Exception as conn_check_error:
                    # 检查连接状态时出错，可能是连接已损坏
                    logger.warning(
                        f"[Thread-{thread_id}] Error checking connection status: {str(conn_check_error)}"
                    )
                    # 继续尝试使用连接，让下面的代码处理可能的错误

                # 尝试执行查询测试连接
                with current_conn_obj.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                DBConnectionManager.db_conn_last_active = time.time()
                logger.debug(f"[Thread-{thread_id}] DB connection test successful.")
                return True
            except (
                AttributeError,
                mysql.connector.errors.OperationalError,
                mysql.connector.errors.InterfaceError,
                IndexError,
            ) as e:
                # 处理各种连接错误
                error_type = type(e).__name__
                logger.error(
                    f"[Thread-{thread_id}] {error_type} when testing connection: {str(e)}. Connection object might be broken."
                )
                # 强制重新初始化连接
                DBConnectionManager.db_conn = None
                self._init_db_conn()
                return bool(DBConnectionManager.db_conn)

        except Exception as e:
            logger.warning(
                f"[Thread-{thread_id}] DB connection test failed: {str(e)}",
                exc_info=True,
            )

            # 尝试关闭失败的连接对象
            if current_conn_obj is not None:
                try:
                    current_conn_obj.close()
                except Exception:
                    pass

            # 设置为None并重新初始化
            DBConnectionManager.db_conn = None
            self._init_db_conn()

            # 返回新连接尝试的状态
            return bool(DBConnectionManager.db_conn)

    def execute_with_retry(self, operation: Callable[[], Any]) -> Any:
        """异步执行数据库操作，带有增强的重试机制"""
        # 提交到线程池执行
        future = self._executor.submit(self._execute_with_retry, operation)
        return future.result()  # 等待结果

    def _execute_with_retry(self, operation: Callable[[], Any]) -> Any:
        """执行数据库操作，带有增强的重试机制"""
        max_retries = 5  # 增加重试次数
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            try:
                # 检查连接状态
                if not self._check_db_conn():
                    # 如果连接不可用，尝试重新初始化
                    logger.warning("数据库连接不可用，尝试重新初始化")
                    self._init_db_conn()

                    if not self._check_db_conn():
                        raise Exception("数据库连接不可用，重新初始化失败")

                # 执行操作
                result = operation()

                # 操作完成后确保连接仍然有效
                self._check_db_conn()

                return result

            except Exception as e:
                last_error = e
                retry_count += 1

                # 检查是否是SSL相关错误或连接丢失错误
                error_str = str(e).lower()
                is_connection_error = (
                    "lost connection" in error_str
                    or "ssl" in error_str
                    or "connection" in error_str
                    or "timeout" in error_str
                    or "not available" in error_str
                )

                if is_connection_error:
                    # 如果是连接错误，尝试重新初始化连接
                    logger.warning(f"检测到连接错误，尝试重新初始化连接: {error_str}")
                    # 关闭并重新初始化连接
                    if DBConnectionManager.db_conn is not None:
                        try:
                            DBConnectionManager.db_conn.close()
                        except:
                            pass
                    DBConnectionManager.db_conn = None
                    self._init_db_conn()

                logger.warning(
                    f"数据库操作失败 (尝试 {retry_count}/{max_retries}): {str(e)}"
                )

                if retry_count < max_retries:
                    # 根据重试次数增加等待时间
                    wait_time = retry_count * 2  # 每次重试增加等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)  # 等待时间逐次增加
                else:
                    logger.error(f"数据库操作失败，已重试{max_retries}次，放弃操作")
                    raise Exception(
                        f"数据库操作失败，已重试{max_retries}次: {str(last_error)}"
                    )

    @classmethod
    def close_connections(cls) -> None:
        """关闭所有连接和定时器"""
        # 设置关闭标志
        cls.is_shutting_down = True

        # 1. 首先关闭统一定时器管理器，防止定时器继续触发新的操作
        if cls._instance and cls._instance.unified_timer_manager:
            try:
                cls._instance.unified_timer_manager.shutdown()
                logger.info("统一定时器管理器已关闭")
            except Exception as e:
                logger.warning(f"关闭统一定时器管理器失败: {str(e)}")

        # 2. 关闭数据库连接
        if cls.db_conn:
            try:
                # 直接关闭连接
                cls.db_conn.close()
                cls.db_conn = None
                logger.info("自动路径规划数据库连接已关闭")
            except Exception as e:
                logger.warning(f"关闭数据库连接失败: {str(e)}")

        # 3. 关闭线程池
        if cls._instance and cls._instance._executor:
            try:
                cls._instance._executor.shutdown(wait=False)
                logger.info("数据库连接管理器线程池已关闭")
            except Exception as e:
                logger.warning(f"关闭数据库连接管理器线程池失败: {str(e)}")

        # 4. 清理连接池
        try:
            # 清理连接池
            if hasattr(mysql.connector.pooling, "_CONNECTION_POOLS"):
                pools = mysql.connector.pooling._CONNECTION_POOLS
                logger.info(f"关闭时发现 {len(pools)} 个连接池")

                # 遍历所有连接池并尝试关闭
                for pool_name, pool in list(pools.items()):
                    try:
                        logger.info(f"尝试清理连接池: {pool_name}")
                        # 关闭池中的所有连接
                        if hasattr(pool, "_queue_free"):
                            free_conns = list(pool._queue_free)
                            for conn in free_conns:
                                try:
                                    if conn and hasattr(conn, "close"):
                                        conn.close()
                                except:
                                    pass

                        # 从全局池字典中移除
                        mysql.connector.pooling._CONNECTION_POOLS.pop(pool_name, None)
                        logger.info(f"成功清理连接池: {pool_name}")
                    except Exception as pool_error:
                        logger.error(f"清理连接池 {pool_name} 失败: {str(pool_error)}")

                # 尝试完全清空连接池字典
                try:
                    mysql.connector.pooling._CONNECTION_POOLS.clear()
                    logger.info("已清空所有连接池字典")
                except:
                    pass
        except Exception as e:
            logger.error(f"清理连接池失败: {str(e)}")

        # 等待一小段时间，确保所有资源都已释放
        time.sleep(0.5)
