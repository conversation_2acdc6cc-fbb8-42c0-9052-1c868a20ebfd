{
    "tasks": [
        {
            "type": "cppbuild",
            "label": "C/C++: g++.exe build project with <PERSON><PERSON>per2",
            "command": "D:\\Program Files\\mingw64\\bin\\g++.exe",
            "args": [
                "-fdiagnostics-color=always",
                "-g",
                "-std=c++17",
                // 1. Clipper2 include path
                "-I${workspaceFolder}/libs/clipper2/include",
                // 2. Your project's .cpp files
                //    Ensure you have a main.cpp or similar file with the main() function.
                //    If your main function is in another file, replace main.cpp with that filename.
                //    If you don't have a main.cpp yet for testing, you'll need to create one.
                "${workspaceFolder}/main.cpp",
                "${workspaceFolder}/Map3D.cpp",
                "${workspaceFolder}/OccupancyMap.cpp",
                "${workspaceFolder}/JPS.cpp",
                // Future .cpp files from your project will be added here
                // 3. Clipper2's .cpp files
                // "${workspaceFolder}/libs/clipper2/src/clipper.core.cpp",
                "${workspaceFolder}/libs/clipper2/src/clipper.engine.cpp",
                "${workspaceFolder}/libs/clipper2/src/clipper.offset.cpp",
                "${workspaceFolder}/libs/clipper2/src/clipper.rectclip.cpp",
                // 4. Output executable
                "-o",
                "${workspaceFolder}/Output.exe"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "problemMatcher": [
                "$gcc"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "detail": "Builds the project including Clipper2 library."
        }
    ],
    "version": "2.0.0"
}