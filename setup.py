#!/usr/bin/env python3
"""
Setup script for Multi-Agent Pathfinding C++ Python bindings
使用 pybind11 将 C++ JPS 算法绑定到 Python
"""

import sys
from pathlib import Path
from pybind11.setup_helpers import Pybind11Extension, build_ext
import pybind11
from setuptools import setup, find_packages

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.absolute()
CPP_ROOT = PROJECT_ROOT / "cpp_implementation"
INCLUDE_DIR = CPP_ROOT / "include"
SRC_DIR = CPP_ROOT / "src"
BINDINGS_DIR = PROJECT_ROOT / "python_bindings"

# 检查必要的目录是否存在
if not INCLUDE_DIR.exists():
    raise FileNotFoundError(f"C++ include directory not found: {INCLUDE_DIR}")
if not SRC_DIR.exists():
    raise FileNotFoundError(f"C++ source directory not found: {SRC_DIR}")

# 收集 C++ 源文件
cpp_sources = []

# 添加 C++ 实现源文件
cpp_source_files = ["JPS.cpp", "Map3D.cpp", "OccupancyMap.cpp"]

for source_file in cpp_source_files:
    source_path = SRC_DIR / source_file
    if source_path.exists():
        # 使用相对路径
        relative_path = source_path.relative_to(PROJECT_ROOT)
        cpp_sources.append(str(relative_path).replace("\\", "/"))
    else:
        print(f"Warning: C++ source file not found: {source_path}")

print(f"Found C++ sources so far: {cpp_sources}")

# 编译选项
compile_args = []
link_args = []

# 根据平台设置编译选项
if sys.platform.startswith("win"):
    # Windows (MSVC)
    compile_args.extend(
        [
            "/std:c++17",  # C++17 标准
            "/O2",  # 优化
            "/W3",  # 警告级别
            "/EHsc",  # 异常处理
            "/DNOMINMAX",  # 避免 Windows.h 的 min/max 宏冲突
            "/D_USE_MATH_DEFINES",  # 启用数学常量定义
            "/utf-8",  # 使用 UTF-8 编码处理源文件
            "/bigobj",  # 支持大对象文件
        ]
    )
else:
    # Linux/macOS (GCC/Clang)
    compile_args.extend(
        [
            "-std=c++17",  # C++17 标准
            "-O3",  # 优化
            "-Wall",  # 警告
            "-Wextra",  # 额外警告
            "-fPIC",  # 位置无关代码
            "-DWITH_THREAD",  # 线程支持
        ]
    )

# 包含目录
include_dirs = [
    str(INCLUDE_DIR.relative_to(PROJECT_ROOT)).replace("\\", "/"),  # C++ 头文件目录
    str(BINDINGS_DIR.relative_to(PROJECT_ROOT)).replace("\\", "/"),  # 绑定头文件目录
    pybind11.get_include(),  # pybind11 头文件
]

# 检查 clipper2 库
clipper2_dir = CPP_ROOT / "libs" / "clipper2"
clipper2_include_dir = clipper2_dir / "include"
clipper2_src_dir = clipper2_dir / "src"

if clipper2_dir.exists() and clipper2_include_dir.exists():
    print(f"Found clipper2 library at: {clipper2_dir}")
    # 添加 clipper2 包含目录
    relative_clipper2_include = clipper2_include_dir.relative_to(PROJECT_ROOT)
    include_dirs.append(str(relative_clipper2_include).replace("\\", "/"))

    # 添加 clipper2 源文件
    clipper2_sources = [
        "clipper.engine.cpp",
        "clipper.offset.cpp",
        "clipper.rectclip.cpp",
    ]

    for src_file in clipper2_sources:
        src_path = clipper2_src_dir / src_file
        if src_path.exists():
            # 使用相对路径
            relative_path = src_path.relative_to(PROJECT_ROOT)
            cpp_sources.append(str(relative_path).replace("\\", "/"))
            print(f"Added clipper2 source: {src_file}")
        else:
            print(f"Warning: clipper2 source not found: {src_path}")
else:
    print(f"Warning: clipper2 library not found at: {clipper2_dir}")

# 添加 pybind11 绑定源文件 (最后添加，确保输出目录正确)
bindings_sources = ["bindings.cpp"]

for binding_file in bindings_sources:
    binding_path = BINDINGS_DIR / binding_file
    if binding_path.exists():
        # 使用相对路径
        relative_path = binding_path.relative_to(PROJECT_ROOT)
        cpp_sources.append(str(relative_path).replace("\\", "/"))
        print(f"Added binding file: {binding_file}")
    else:
        print(f"Warning: Binding file not found: {binding_path}")

print(f"Final C++ sources list: {cpp_sources}")

# 定义扩展模块
ext_modules = [
    Pybind11Extension(
        "pathfinding_cpp",  # 模块名
        sources=cpp_sources,  # 源文件列表
        include_dirs=include_dirs,
        language="c++",
        cxx_std=17,  # C++17 标准
        extra_compile_args=compile_args,
        extra_link_args=link_args,
    ),
]

# 读取 README 文件作为长描述
readme_path = PROJECT_ROOT / "README.md"
long_description = ""
if readme_path.exists():
    with open(readme_path, "r", encoding="utf-8") as f:
        long_description = f.read()
else:
    long_description = (
        "Multi-Agent Pathfinding with JPS algorithm - C++ Python bindings"
    )

# 读取版本信息
version = "0.1.0"

# 设置包信息
setup(
    name="pathfinding-cpp",
    version=version,
    author="Your Name",
    author_email="<EMAIL>",
    description="Multi-Agent Pathfinding with JPS algorithm - C++ Python bindings",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/Multi_agent_pathfinding_cpp",
    # 包配置
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    # C++ 扩展模块
    ext_modules=ext_modules,
    # 自定义构建命令
    cmdclass={"build_ext": build_ext},
    # Python 版本要求
    python_requires=">=3.7",
    # 依赖包
    install_requires=[
        "pybind11>=2.6.0",
        "numpy>=1.19.0",
    ],
    # 开发依赖
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov",
            "black",
            "flake8",
        ],
        "test": [
            "pytest>=6.0",
            "pytest-cov",
        ],
    },
    # 分类信息
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: C++",
        "Topic :: Scientific/Engineering",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    # 关键词
    keywords="pathfinding, jps, jump-point-search, multi-agent, cpp, pybind11",
    # 项目链接
    project_urls={
        "Bug Reports": "https://github.com/yourusername/Multi_agent_pathfinding_cpp/issues",
        "Source": "https://github.com/yourusername/Multi_agent_pathfinding_cpp",
        "Documentation": "https://github.com/yourusername/Multi_agent_pathfinding_cpp/wiki",
    },
    # 包含数据文件
    include_package_data=True,
    zip_safe=False,
)
