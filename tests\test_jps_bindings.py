#!/usr/bin/env python3
"""
测试 JPS C++ 绑定的 Python 接口
Test JPS C++ bindings Python interface
"""

import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, "src"))

# 确保工作目录正确，以便找到 .pyd 文件
original_cwd = os.getcwd()
os.chdir(project_root)

print(f"项目根目录: {project_root}")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python 路径: {sys.path[:3]}...")  # 只显示前3个路径


def test_jps():
    """主测试函数，模拟 C++ 中的 test_jps() 函数"""
    print("=" * 50)
    print("  JPS Python 绑定测试")
    print("=" * 50)

    try:
        # 尝试导入模块
        print("正在导入 pathfinding_cpp 模块...")
        import pathfinding_cpp

        # from pathfinding_cpp import Map3D

        print(f"✓ 成功导入 pathfinding_cpp 模块")
        print(f"  版本: {pathfinding_cpp.__version__}")

        # 创建测试环境
        print("\n正在创建测试环境...")
        converter = pathfinding_cpp.create_grid_converter(
            lat_size=0.0001,
            lon_size=0.0001,
            alt_size=1.0,
            min_lat=30.0,
            min_lon=120.0,
            min_alt=0.0,
        )
        print("✓ 创建了 GridConverter")

        height, width, depth = 100, 100, 10
        map3d = pathfinding_cpp.Map3D(height, width, depth, converter)
        print(f"✓ 创建了 Map3D，尺寸: {width}x{height}x{depth}")

        occupancy_map = pathfinding_cpp.OccupancyMap(height, width, depth, 4)
        print("✓ 创建了 OccupancyMap")

        jps = pathfinding_cpp.JPS(
            map_data=map3d,
            occupancy_map_data=occupancy_map,
            takeoff_speed=1.0,
            cruise_speed=2.0,
            landing_speed=1.0,
            max_steps=10000,
            need_smooth=True,
            smoothness=3.0,
            heuristic_weight=1.0,
            jump_step_size=1,
        )
        print("✓ 创建了 JPS 实例")

        # 测试 1: 简单路径规划
        print("\n测试 1: 简单路径规划（无障碍物）")
        start = (10, 10, 5)
        goal = (90, 90, 5)
        print(f"  起点: {start}")
        print(f"  终点: {goal}")

        result = jps.find_path(start, goal, 5, "TEST_AGENT", 0)

        if result["success"]:
            print(f"✓ 路径规划成功")
            print(f"  路径点数量: {len(result['path'])}")
            if result["path"]:
                print(f"  起点: {result['path'][0]}")
                print(f"  终点: {result['path'][-1]}")
            if result["turn_path"]:
                print(f"  转弯点数量: {len(result['turn_path'])}")
        else:
            print(f"✗ 路径规划失败: {result['error']}")

        # 测试 2: 基本类型测试
        print("\n测试 2: 基本类型测试")

        # 测试 GridNode3D
        node = pathfinding_cpp.GridNode3D(10.0, 20.0, 5.0, 100)
        print(f"✓ GridNode3D: {repr(node)}")

        # 测试坐标转换
        grid_coords = converter.geographic_to_grid(30.001, 120.001, 5.0)
        print(f"✓ 地理坐标转栅格坐标: {grid_coords}")

        geo_coords = converter.grid_to_geographic(10.0, 10.0, 5.0)
        print(f"✓ 栅格坐标转地理坐标: {geo_coords}")

        # 测试地图操作
        is_traversable = map3d.is_traversable((50, 50, 5))
        print(f"✓ 地图可通行性检查: {is_traversable}")

        print("\n所有测试完成!")
        return result["success"] if "result" in locals() else True

    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        print("请确保已正确编译 pathfinding_cpp 模块")
        return False
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        # 直接运行测试
        success = test_jps()
        print(f"\n测试结果: {'成功' if success else '失败'}")
        sys.exit(0 if success else 1)
    finally:
        # 恢复原始工作目录
        if "original_cwd" in globals():
            os.chdir(original_cwd)
