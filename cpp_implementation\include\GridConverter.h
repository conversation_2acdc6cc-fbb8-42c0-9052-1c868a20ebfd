// 地理坐标与栅格坐标转换器
#pragma once

#include <tuple> // 引入std::tuple用于坐标表示
#include <cmath> // 引入数学函数：std::cos、std::abs等

// 地理坐标结构体，表示位置的经纬度和高度
struct GeoCoordinate
{
    double lat; // 纬度
    double lon; // 经度
    double alt; // 高度
};

// 栅格分辨率结构体，定义栅格单元的尺寸
struct GridCellSize
{
    double lat_resolution; // 纬度方向分辨率
    double lon_resolution; // 经度方向分辨率
    double alt_resolution; // 高度方向分辨率
};

// 栅格坐标类型，使用浮点数表示三维位置(x, y, z)
using GridPoint = std::tuple<float, float, float>;

class GridConverter
{
public:
    static constexpr double EARTH_RADIUS_METERS = 6371000.0; // 地球半径（米）
    static constexpr double PI = 3.14159265358979323846;     // 圆周率
    static constexpr double DEG_TO_RAD = PI / 180.0;         // 角度转弧度的转换系数

    // 栅格转换器构造函数
    // @param cell_size: 栅格单元的分辨率配置
    // @param min_coords: 地图左下角的地理坐标（最小坐标）
    GridConverter(const GridCellSize &cell_size, const GeoCoordinate &min_coords)
        : cell_size_(cell_size), min_coords_(min_coords) {}

    // 将地理坐标（经纬度）转换为栅格坐标
    // @param geo_coord: 输入的地理坐标
    // @return: 返回对应的栅格坐标（浮点数）
    GridPoint geographic_to_grid(const GeoCoordinate &geo_coord) const
    {
        float grid_x = static_cast<float>((geo_coord.lon - min_coords_.lon) / cell_size_.lon_resolution); // 经度方向
        float grid_y = static_cast<float>((geo_coord.lat - min_coords_.lat) / cell_size_.lat_resolution); // 纬度方向
        float grid_z = geographic_alt_to_grid_alt(geo_coord.alt);                                         // 高度方向
        return std::make_tuple(grid_x, grid_y, grid_z);
    }

    // 将栅格坐标转换回地理坐标
    // @param grid_coord: 输入的栅格坐标
    // @return: 返回对应的地理坐标
    GeoCoordinate grid_to_geographic(const GridPoint &grid_coord) const
    {
        double geo_lon = static_cast<double>(std::get<0>(grid_coord)) * cell_size_.lon_resolution + min_coords_.lon; // 计算经度
        double geo_lat = static_cast<double>(std::get<1>(grid_coord)) * cell_size_.lat_resolution + min_coords_.lat; // 计算纬度
        double geo_alt = grid_alt_to_geographic_alt(std::get<2>(grid_coord));                                        // 计算高度
        return {geo_lat, geo_lon, geo_alt};
    }

    // 将栅格坐标转换为地理坐标的重载版本
    // @param grid_x: 栅格X坐标
    // @param grid_y: 栅格Y坐标
    // @param grid_z: 栅格Z坐标
    // @return: 返回对应的地理坐标
    GeoCoordinate grid_to_geographic(float grid_x, float grid_y, float grid_z) const
    {
        return grid_to_geographic(std::make_tuple(grid_x, grid_y, grid_z));
    }

    // 将地理高度转换为栅格高度
    // @param geo_alt: 地理高度（double类型）
    // @return: 返回栅格高度（float类型）
    double geographic_alt_to_grid_alt(double geo_alt) const
    {
        return static_cast<float>((geo_alt - min_coords_.alt) / cell_size_.alt_resolution);
    }

    // 将栅格高度转换为地理高度
    // @param grid_z: 栅格高度（float类型）
    // @return: 返回地理高度（double类型）
    double grid_alt_to_geographic_alt(float grid_z) const
    {
        return static_cast<double>(grid_z) * cell_size_.alt_resolution + min_coords_.alt;
    }

    // 计算指定纬度下经度方向上每个栅格的实际距离（米）
    // @param at_latitude_deg: 指定的纬度（度）
    // @return: 返回每个栅格的实际距离（米）
    double get_meters_per_grid_x(double at_latitude_deg) const
    {
        if (std::abs(cell_size_.lon_resolution) < 1e-12)
            return 0.0; // 处理分辨率接近零的情况
        double lat_rad = at_latitude_deg * DEG_TO_RAD;
        double meters_per_degree_lon = EARTH_RADIUS_METERS * DEG_TO_RAD * std::cos(lat_rad);
        return cell_size_.lon_resolution * meters_per_degree_lon;
    }

    // 计算纬度方向上每个栅格的实际距离（米）
    // @return: 返回每个栅格的实际距离（米）
    double get_meters_per_grid_y() const
    {
        if (std::abs(cell_size_.lat_resolution) < 1e-12)
            return 0.0;                                                  // 处理分辨率接近零的情况
        double meters_per_degree_lat = EARTH_RADIUS_METERS * DEG_TO_RAD; // 约111.32千米/度
        return cell_size_.lat_resolution * meters_per_degree_lat;
    }

    // 将实际距离转换为经度方向上的栅格数量
    // @param meters: 实际距离（米）
    // @param at_latitude_deg: 指定的纬度（度）
    // @return: 返回对应的栅格数量（浮点数）
    float convert_meters_to_grids_x(double meters, double at_latitude_deg) const
    {
        double m_per_grid_x = get_meters_per_grid_x(at_latitude_deg);
        return (std::abs(m_per_grid_x) > 1e-9) ? static_cast<float>(meters / m_per_grid_x) : 0.0f;
    }

    // 将实际距离转换为纬度方向上的栅格数量
    // @param meters: 实际距离（米）
    // @return: 返回对应的栅格数量（浮点数）
    float convert_meters_to_grids_y(double meters) const
    {
        double m_per_grid_y = get_meters_per_grid_y();
        return (std::abs(m_per_grid_y) > 1e-9) ? static_cast<float>(meters / m_per_grid_y) : 0.0f;
    }

    // 使用纬度方向的分辨率将半径（米）转换为栅格数量
    // @param radius_meters: 半径（米）
    // @return: 返回对应的栅格数量（浮点数）
    float convert_radius_meters_to_grids_using_lat_resolution(double radius_meters) const
    {
        return convert_meters_to_grids_y(radius_meters);
    }

    // 将实际距离转换为平均栅格数量（考虑经纬度两个方向）
    // @param meters: 实际距离（米）
    // @param at_latitude_deg: 指定的纬度（度）
    // @return: 返回平均栅格数量（浮点数）
    float convert_meters_to_average_grids(double meters, double at_latitude_deg) const
    {
        double m_per_grid_x = get_meters_per_grid_x(at_latitude_deg);
        double m_per_grid_y = get_meters_per_grid_y();

        // 处理特殊情况
        if (std::abs(m_per_grid_x) < 1e-9 && std::abs(m_per_grid_y) < 1e-9)
            return 0.0f;
        // 处理单一方向的情况
        if (std::abs(m_per_grid_x) < 1e-9)
            return static_cast<float>(meters / m_per_grid_y);
        if (std::abs(m_per_grid_y) < 1e-9)
            return static_cast<float>(meters / m_per_grid_x);

        // 计算平均值
        double avg_m_per_grid = (m_per_grid_x + m_per_grid_y) / 2.0;
        return static_cast<float>(meters / avg_m_per_grid);
    }

    // 获取栅格单元尺寸的访问器方法
    // @return: 返回栅格单元尺寸的常量引用
    const GridCellSize &get_cell_size() const { return cell_size_; }

private:
    GridCellSize cell_size_;
    GeoCoordinate min_coords_;
};
