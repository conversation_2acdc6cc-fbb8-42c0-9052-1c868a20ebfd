#include "JPS.h"
#include <cmath>     // 用于数学函数：fabs、std::round、std::sqrt等
#include <algorithm> // 用于算法函数：std::min、std::max、std::sort、std::reverse
#include <sstream>   // 用于字符串流处理：std::ostringstream
#include <set>       // 用于std::set存储不重复的邻居方向
#include <vector>
#include <queue> // 用于std::priority_queue在_find_cruise_path中的实现

// GridNode3D类的属性说明:
// int x, y, z;        // 三维网格坐标
// long long t;        // 时间戳
// double g, h, f;     // A*算法的代价值：g(起点到当前点代价)、h(当前点到终点预估代价)、f(总代价)
// GridNode3D* parent; // 父节点指针，用于路径回溯
// int jump_step;      // 跳跃步长
// bool need_sight;    // 是否需要视线检查
// 构造函数形式：GridNode3D(int x, int y, int z, long long time = 0, double g_val = 0.0)

// 坐标系转换说明：
// 1. 坐标系转换：Python的(y, x, z)对应C++的(x, y, z)
// 注意：在Python中，y对应height，x对应width；而在C++中，x对应width，y对应height
// 为了更清晰，方向向量在C++中也使用(dy, dx, dz)表示，与Python保持一致
// 2. 点坐标转换：
//    - Python格式：(py_y, py_x, py_z)
//    - C++格式：Point3D和GridNode3D使用(cpp_x, cpp_y, cpp_z)
// 3. 映射关系：
//    cpp_x = py_x（Python的x对应C++的x）
//    cpp_y = py_y（Python的y对应C++的y）
//    cpp_z = py_z（Python的z对应C++的z）

const std::vector<JPS::Direction3D> JPS::DIRECTION_PRIORITIES = {
    // 第一优先级：XY平面基本方向（水平移动）
    // 虽然C++中存储为{dx, dy, dz}，但为了清晰，我们将其视为{dy, dx, dz}
    {1, 0, 0},  // 向右移动，对应Python中的(0, 1, 0)，表示dy=0, dx=1, dz=0
    {-1, 0, 0}, // 向左移动，对应Python中的(0, -1, 0)，表示dy=0, dx=-1, dz=0
    {0, 1, 0},  // 向前移动，对应Python中的(1, 0, 0)，表示dy=1, dx=0, dz=0
    {0, -1, 0}, // 向后移动，对应Python中的(-1, 0, 0)，表示dy=-1, dx=0, dz=0
    // 第二优先级：XY平面对角线移动（斜向移动）
    {1, 1, 0},   // 右前移动，对应Python中的(1, 1, 0)，表示dy=1, dx=1, dz=0
    {-1, 1, 0},  // 左前移动，对应Python中的(1, -1, 0)，表示dy=-1, dx=1, dz=0
    {1, -1, 0},  // 右后移动，对应Python中的(-1, 1, 0)，表示dy=1, dx=-1, dz=0
    {-1, -1, 0}, // 左后移动，对应Python中的(-1, -1, 0)，表示dy=-1, dx=-1, dz=0
    // 第三优先级：Z轴垂直移动
    {0, 0, 1}, // 垂直向上，对应Python中的(0, 0, 1)，表示dy=0, dx=0, dz=1
    {0, 0, -1} // 垂直向下，对应Python中的(0, 0, -1)，表示dy=0, dx=0, dz=-1
};

const std::map<JPS::Direction3D, std::vector<JPS::Direction3D>> JPS::FORCED_NEIGHBOR_DIRS = {
    // 坐标系转换说明:
    // Python坐标 (dy, dx, dz) 转换为 C++坐标 (dx, dy, dz)
    // 但为了清晰，我们在注释中将C++坐标也表示为 (dy, dx, dz)

    // 向后移动：Python (-1,0,0) [py_dy=-1] -> C++ (0,-1,0) [cpp_dy=-1]
    {{0, -1, 0}, {
                     // 沿-Y轴向后移动时的强制邻居检查方向
                     {1, 0, 0},  // 检查右侧 (对应Python的(0,1,0))
                     {-1, 0, 0}, // 检查左侧 (对应Python的(0,-1,0))
                     {0, 0, 1},  // 检查上方 (对应Python的(0,0,1))
                     {0, 0, -1}  // 检查下方 (对应Python的(0,0,-1))
                 }},
    // 向前移动：Python (1,0,0) [py_dy=1] -> C++ (0,1,0) [cpp_dy=1]
    {{0, 1, 0}, {
                    // 沿+Y轴向前移动时的强制邻居检查方向
                    {1, 0, 0},  // 检查右侧
                    {-1, 0, 0}, // 检查左侧
                    {0, 0, 1},  // 检查上方
                    {0, 0, -1}  // 检查下方
                }},
    // 向左移动：Python (0,-1,0) [py_dx=-1] -> C++ (-1,0,0) [cpp_dx=-1]
    {{-1, 0, 0}, {
                     // 沿-X轴向左移动时的强制邻居检查方向
                     {0, 1, 0},  // 检查前方 (对应Python的(1,0,0))
                     {0, -1, 0}, // 检查后方 (对应Python的(-1,0,0))
                     {0, 0, 1},  // 检查上方
                     {0, 0, -1}  // 检查下方
                 }},
    // 向右移动：Python (0,1,0) [py_dx=1] -> C++ (1,0,0) [cpp_dx=1]
    {{1, 0, 0}, {
                    // 沿+X轴向右移动时的强制邻居检查方向
                    {0, 1, 0},  // 检查前方
                    {0, -1, 0}, // 检查后方
                    {0, 0, 1},  // 检查上方
                    {0, 0, -1}  // 检查下方
                }},
    // 向下移动：Python (0,0,-1) -> C++ (0,0,-1)
    {{0, 0, -1}, {
                     // 沿-Z轴向下移动时的强制邻居检查方向
                     {0, 1, 0},  // 检查前方
                     {0, -1, 0}, // 检查后方
                     {1, 0, 0},  // 检查右侧
                     {-1, 0, 0}  // 检查左侧
                 }},
    // 向上移动：Python (0,0,1) -> C++ (0,0,1)
    {{0, 0, 1}, {
                    // 沿+Z轴向上移动时的强制邻居检查方向
                    {0, 1, 0},  // 检查前方
                    {0, -1, 0}, // 检查后方
                    {1, 0, 0},  // 检查右侧
                    {-1, 0, 0}  // 检查左侧
                }},
    // 对角线方向移动：
    // 右前方向：Python (1,1,0) [py_dy=1,py_dx=1] -> C++ (1,1,0) [cpp_dx=1,cpp_dy=1]
    {{1, 1, 0}, {
                    // 右前方向移动时的强制邻居检查方向
                    {0, 0, 1},  // 检查上方
                    {0, 0, -1}, // 检查下方
                    {0, 1, 0},  // 检查前向分量 (对应Python的(1,0,0))
                    {1, 0, 0}   // 检查右向分量 (对应Python的(0,1,0))
                }},
    // 左前方向：Python (1,-1,0) [py_dy=1,py_dx=-1] -> C++ (-1,1,0) [cpp_dx=-1,cpp_dy=1]
    {{-1, 1, 0}, {
                     // 左前方向移动时的强制邻居检查方向
                     {0, 0, 1},  // 检查上方
                     {0, 0, -1}, // 检查下方
                     {0, 1, 0},  // 检查前向分量
                     {-1, 0, 0}  // 检查左向分量
                 }},
    // 右后方向：Python (-1,1,0) [py_dy=-1,py_dx=1] -> C++ (1,-1,0) [cpp_dx=1,cpp_dy=-1]
    {{1, -1, 0}, {
                     // 右后方向移动时的强制邻居检查方向
                     {0, 0, 1},  // 检查上方
                     {0, 0, -1}, // 检查下方
                     {0, -1, 0}, // 检查后向分量
                     {1, 0, 0}   // 检查右向分量
                 }},
    // 左后方向：Python (-1,-1,0) [py_dy=-1,py_dx=-1] -> C++ (-1,-1,0) [cpp_dx=-1,cpp_dy=-1]
    {{-1, -1, 0}, {
                      // 左后方向移动时的强制邻居检查方向
                      {0, 0, 1},  // 检查上方
                      {0, 0, -1}, // 检查下方
                      {0, -1, 0}, // 检查后向分量
                      {-1, 0, 0}  // 检查左向分量
                  }}};

JPS::JPS(Map3D *map_data,
         OccupancyMap *occupancy_map_data,
         double takeoff_speed,    // 起飞速度
         double cruise_speed,     // 巡航速度
         double landing_speed,    // 降落速度
         int max_steps,           // 最大步数限制
         bool need_smooth,        // 是否需要路径平滑
         double smoothness,       // 平滑度参数
         double heuristic_weight, // 启发式函数权重
         int jump_step_size)      // 跳跃步长（原vertical_look_steps）
    : map_(map_data),
      occupancy_map_(occupancy_map_data),
      width_(0), height_(0), depth_(0),
      takeoff_speed_t_(0.0), cruise_speed_t_(0.0), landing_speed_t_(0.0),
      max_steps_(max_steps),
      need_smooth_(need_smooth),
      smoothness_(smoothness),
      heuristic_weight_(heuristic_weight),    // 初始化启发式权重
      default_jump_step_size_(jump_step_size) // 初始化默认跳跃步长
{
    if (!map_data)
    {
        throw std::invalid_argument("地图数据不能为空");
    }
    width_ = map_->get_width();
    height_ = map_->get_height();
    depth_ = map_->get_depth();

    if (takeoff_speed <= 0 || cruise_speed <= 0 || landing_speed <= 0)
    {
        throw std::invalid_argument("速度值必须为正数");
    }
    if (max_steps <= 0)
    {
        throw std::invalid_argument("最大步数必须为正数");
    }
    if (smoothness < 0)
    {
        throw std::invalid_argument("平滑度参数不能为负数");
    }
    if (heuristic_weight_ <= 0)
    {
        throw std::invalid_argument("启发式函数权重必须为正数");
    }
    if (default_jump_step_size_ < 1)
    {
        throw std::invalid_argument("跳跃步长必须大于等于1");
    }

    // 计算时间步长（时间 = 距离/速度，所以时间步长 = 1/速度）
    takeoff_speed_t_ = 1.0 / takeoff_speed; // 起飞阶段时间步长
    cruise_speed_t_ = 1.0 / cruise_speed;   // 巡航阶段时间步长
    landing_speed_t_ = 1.0 / landing_speed; // 降落阶段时间步长
}

std::tuple<std::optional<std::vector<GridNode3D>>, std::optional<std::string>>
JPS::_vertical_takeoff(
    const Point3D &start_pos_xyz, // 起始位置(x, y, z)
    int min_height_z,             // 起飞目标高度
    const std::string &agent_id,  // 智能体ID
    long long current_time)       // 当前时间
// const std::vector<Constraint>& constraints) // 约束条件（预留）
{
    std::vector<GridNode3D> path;               // 存储垂直起飞路径
    long long time_at_current_z = current_time; // 当前高度的时间戳

    // 提取起始位置的三维坐标
    int start_x = std::get<0>(start_pos_xyz);
    int start_y = std::get<1>(start_pos_xyz);
    int start_z = std::get<2>(start_pos_xyz);

    // 参考Python实现:
    // step_size = 5
    // heights = list(range(start[2], min_height + 1, step_size))
    // if min_height not in heights: heights.append(min_height)
    // 这里min_height_z是目标高度，start_z是当前高度
    // 需要从start_z逐步上升到min_height_z

    const int jump_step_size = 5;      // 垂直方向的步进大小
    std::vector<int> heights_to_check; // 需要检查的高度列表

    // 如果已经达到或超过最小高度要求
    if (start_z >= min_height_z)
    {
        // 已在目标高度或更高位置，仅添加当前位置作为路径点
        // 注意：虽然这种情况通常应在调用_vertical_takeoff前处理
        // 因为_vertical_takeoff通常在start[2] < min_height时调用
        // 但为了安全起见，如果遇到这种情况，直接返回起始点
        Point3D current_pt = {start_x, start_y, start_z};
        auto validity = _is_valid_position(current_pt, time_at_current_z, min_height_z, agent_id, true);
        if (!validity.first)
        {
            return {std::nullopt, "起飞位置无效: " + validity.second.value_or("")};
        }
        path.emplace_back(start_x, start_y, start_z, time_at_current_z);
        return {path, std::nullopt};
    }

    // 生成从起始高度到目标高度的高度序列
    for (int z = start_z; z < min_height_z; z += jump_step_size)
    {
        heights_to_check.push_back(z);
    }

    // 确保包含目标高度
    if (heights_to_check.empty() || heights_to_check.back() < min_height_z)
    {
        // 处理两种情况：
        // 1. 循环未执行（如start_z接近min_height_z）
        // 2. 最后一步超过了目标高度
        // 这两种情况都需要确保检查min_height_z本身
        bool min_height_present = false;
        for (int h : heights_to_check)
            if (h == min_height_z)
                min_height_present = true;
        if (!min_height_present)
            heights_to_check.push_back(min_height_z);
    }

    // 处理特殊情况：起始高度就是目标高度（如start_z = 0, min_height = 0）
    if (heights_to_check.empty() && start_z == min_height_z)
    {
        heights_to_check.push_back(start_z);
    }

    GridNode3D *prev_node_ptr = nullptr; // 用于记录上一个节点

    // 遍历每个高度，生成路径点
    for (int z_coord : heights_to_check)
    {
        Point3D current_level_pos = {start_x, start_y, z_coord};

        // 计算时间戳：
        // 1. Python版本对每个新添加的节点都增加 takeoff_speed_t
        // 2. 第一个节点（z_coord == start_z）使用 current_time
        // 3. 后续节点的时间递增
        if (!path.empty())
        { // 如果不是路径中的第一个点
            // 计算从上一个高度到当前高度的时间增量
            int dz = z_coord - path.back().z;
            time_at_current_z += static_cast<long long>(std::round(static_cast<double>(std::abs(dz)) * takeoff_speed_t_));
        }
        else
        {
            // 对于第一个点：
            // - time_at_current_z已初始化为current_time
            // - 如果是第一个点且z_coord == start_z，使用current_time
            // - 如果z_coord != start_z（可能heights_to_check的第一个元素不是start_z），
            //   需要计算从start_z到当前z_coord的时间
            if (z_coord != start_z)
            {                               // 如果不是初始高度
                int dz = z_coord - start_z; // 计算高度差
                time_at_current_z = current_time + static_cast<long long>(std::round(static_cast<double>(std::abs(dz)) * takeoff_speed_t_));
            }
            else
            {
                time_at_current_z = current_time; // 初始点使用当前时间
            }
        }

        // 检查位置的有效性（ignore_min_height=true，因为正在上升过程中）
        auto validity = _is_valid_position(current_level_pos, time_at_current_z, min_height_z, agent_id, true);
        if (!validity.first)
        {
            std::ostringstream oss;
            // 可以通过地图转换器获取实际地理高度
            // GeoCoordinate real_geo_z = map_->get_converter().grid_to_geographic(0,0, static_cast<float>(z_coord));
            oss << "垂直起飞在高度z=" << z_coord // << " (实际高度: " << real_geo_z.alt << ")"
                << "失败。原因: " << validity.second.value_or("未知碰撞");
            return {std::nullopt, oss.str()};
        }

        // 创建当前高度的节点
        GridNode3D current_node(start_x, start_y, z_coord, time_at_current_z);
        if (prev_node_ptr && !path.empty())
        {
            // 注意：不直接设置父节点指针，因为vector重新分配可能导致指针失效
            // current_node.parent = &path.back();
            // 为简单起见，返回节点列表时可能不严格需要父指针
            // 因为路径可以按顺序重建。如果需要，应仔细管理内存或使用索引
        }
        path.push_back(current_node);
        // 同样不安全，因为vector可能重新分配
        // prev_node_ptr = &path.back();
    }

    // 处理路径为空但不应为空的情况：
    // 1. start_z < min_height_z但循环未执行
    // 2. min_height_z == start_z且heights_to_check未正确填充
    // 注意：Python代码确保heights中包含start[2]作为第一个元素
    if (path.empty() && start_z <= min_height_z)
    {
        // heights_to_check的逻辑可能有问题，至少添加起始点
        Point3D initial_pos = {start_x, start_y, start_z};
        auto validity = _is_valid_position(initial_pos, current_time, min_height_z, agent_id, true);
        if (!validity.first)
        {
            return {std::nullopt, "起始起飞位置无效: " + validity.second.value_or("")};
        }
        path.emplace_back(start_x, start_y, start_z, current_time);
    }

    return {path, std::nullopt};
}

std::tuple<std::optional<std::vector<GridNode3D>>, std::optional<std::string>>
JPS::_vertical_landing(
    const GridNode3D &last_cruise_node, // 巡航阶段最后一个节点
    const Point3D &goal_pos_xyz,        // 目标位置(x, y, z)
    const std::string &agent_id)        // 智能体ID
// const std::vector<Constraint>& constraints) // 约束条件（预留）
{
    std::vector<GridNode3D> path;                     // 存储降落路径
    long long time_at_current_z = last_cruise_node.t; // 从巡航结束时间开始

    // 提取目标位置坐标
    int goal_x = std::get<0>(goal_pos_xyz);
    int goal_y = std::get<1>(goal_pos_xyz);
    int goal_z = std::get<2>(goal_pos_xyz);

    // 记录降落起始高度
    int initial_landing_z = last_cruise_node.z;

    // 参考Python实现：
    // step_size = -5 (负值表示向下移动)
    // heights = list(range(last_cruise_node.z - 1, goal[2] - 1, step_size))
    // if goal[2] not in heights: heights.append(goal[2])
    // 注意：Python的range右边界是开区间，所以goal[2]-1作为终点实际会下降到goal[2]

    const int jump_step_size = -5;     // 垂直下降的步进大小
    std::vector<int> heights_to_check; // 需要检查的高度列表

    // 如果已经在目标高度或更低位置
    if (initial_landing_z <= goal_z)
    {
        Point3D current_pt_at_cruise_alt = {goal_x, goal_y, initial_landing_z};
        long long current_time_eval = time_at_current_z;

        // 处理特殊情况：
        // 1. 如果initial_landing_z不等于goal_z，我们需要先在initial_landing_z添加一个节点
        //    这发生在起始点的x,y与last_cruise_node不同时
        // 2. 然而，Python的逻辑暗示降落是从last_cruise_node的x,y投影垂直向下的
        //    这里我们使用goal_x, goal_y作为降落轨迹

        // 如果last_cruise_node已经在(goal_x, goal_y, initial_landing_z)
        // 且initial_landing_z == goal_z，那么这就是终点
        // Python代码中path.append(node)表示第一个添加的降落点已经是下降一步后的点
        // current_t = last_cruise_node.t + self.landing_speed_t
        // 这意味着降落路径中的第一个点时间已经前进了一步

        // 特殊情况处理：
        // - 如果initial_landing_z == goal_z，路径就只包含目标点
        // - 从last_cruise_node.t计算到达时间（如果x,y相同则是零高度变化，否则是水平移动）
        // - 对于纯垂直降落，如果initial_landing_z == goal_z，说明last_cruise_node实际上就是目标点
        // - 调用find_path时应处理这种情况
        // - 如果调用此函数，通常意味着last_cruise_node.z > goal_z
        // - 为安全起见，如果initial_landing_z == goal_z：
        if (initial_landing_z == goal_z)
        {
            // 计算最终时间（添加一个小的时间步长表示这一"步"移动）
            long long final_time = time_at_current_z + static_cast<long long>(std::round(1.0 * landing_speed_t_));
            Point3D final_pt = {goal_x, goal_y, goal_z};
            auto validity = _is_valid_position(final_pt, final_time, 0, agent_id, true);
            if (!validity.first)
            {
                return {std::nullopt, "降落终点位置（在目标高度）无效: " + validity.second.value_or("")};
            }
            path.emplace_back(goal_x, goal_y, goal_z, final_time);
            return {path, std::nullopt};
        }
        // 如果initial_landing_z < goal_z（已经低于目标高度，异常情况）
        // 返回错误或直接返回目标点
        return {std::nullopt, "降落起始点低于目标高度"};
    }

    // 从起始高度向下迭代到目标高度
    // Python: range(last_cruise_node.z - 1, goal[2] - 1, step_size)
    // 这意味着：
    // 1. 如果step是-1，第一个高度是last_cruise_node.z - 1
    // 2. 使用step_size = -5时，第一个高度是initial_landing_z + step_size
    //    (例如：initial_landing_z - 5)
    for (int z = initial_landing_z + jump_step_size; z > goal_z; z += jump_step_size)
    {
        heights_to_check.push_back(z);
    }
    // 确保目标高度被包含在列表中
    bool goal_z_present = false;
    if (!heights_to_check.empty())
    {
        // 检查是否已经包含目标高度
        for (int h : heights_to_check)
            if (h == goal_z)
                goal_z_present = true;
    }
    else
    {
        // heights_to_check为空的情况：
        // 当initial_landing_z + jump_step_size <= goal_z时
        // 例如：initial=10, goal=7, jump_step_size=-5
        // 循环条件 z=5; z > 7 为false，所以heights_to_check为空
    }

    // 如果目标高度不在列表中，添加它
    if (!goal_z_present)
    {
        heights_to_check.push_back(goal_z);
        // 降序排序，确保从高到低降落
        std::sort(heights_to_check.rbegin(), heights_to_check.rend());
    }

    // 处理特殊情况：如果列表为空但应该有目标高度
    if (heights_to_check.empty() && initial_landing_z > goal_z)
    { // 这种情况在之前添加goal_z后不应该发生
        heights_to_check.push_back(goal_z);
    }

    // 累计时间从巡航节点开始
    long long accumulated_time = last_cruise_node.t;

    // 遍历每个高度生成路径点
    for (int z_coord : heights_to_check)
    {
        Point3D current_level_pos = {goal_x, goal_y, z_coord};

        // 计算时间增量
        if (!path.empty())
        {
            // 从上一个点到当前高度的时间
            int dz = path.back().z - z_coord;
            accumulated_time += static_cast<long long>(std::round(static_cast<double>(std::abs(dz)) * landing_speed_t_));
        }
        else
        { // 第一个降落点
            // 从起始降落高度到当前高度的时间
            int dz = initial_landing_z - z_coord;
            accumulated_time += static_cast<long long>(std::round(static_cast<double>(std::abs(dz)) * landing_speed_t_));
        }

        // 检查位置有效性（min_height=0，因为在降落过程中）
        auto validity = _is_valid_position(current_level_pos, accumulated_time, 0, agent_id, true);
        if (!validity.first)
        {
            std::ostringstream oss;
            oss << "Vertical landing failed at height z=" << z_coord
                << ". Reason: " << validity.second.value_or("Unknown collision");
            return {std::nullopt, oss.str()};
        }
        path.emplace_back(goal_x, goal_y, z_coord, accumulated_time);
    }

    // 处理路径为空但应该有路径的情况
    if (path.empty() && initial_landing_z > goal_z)
    {
        // 直接计算最终时间和位置
        long long final_time = last_cruise_node.t +
                               static_cast<long long>(std::round(static_cast<double>(initial_landing_z - goal_z) * landing_speed_t_));
        Point3D final_pos = {goal_x, goal_y, goal_z};
        auto validity = _is_valid_position(final_pos, final_time, 0, agent_id, true);
        if (!validity.first)
        {
            return {std::nullopt, "Final landing position (direct path empty) invalid: " + validity.second.value_or("")};
        }
        path.emplace_back(goal_x, goal_y, goal_z, final_time);
    }
    return {path, std::nullopt};
}

// Implementations for _find_cruise_path, _jump, _get_neighbors, etc. will follow.
// find_path will orchestrate these.

// find_path 完整实现 - 路径规划主函数
// 将路径规划分为三个阶段：垂直起飞、水平巡航和垂直降落
std::tuple<
    std::optional<std::vector<GridNode3D>>,      // 完整路径
    std::optional<std::vector<GridNode3D>>,      // 转弯点路径
    std::optional<std::string>>                  // 错误信息
JPS::find_path(const GridNode3D &start_node_ref, // 起始节点
               const GridNode3D &goal_node_ref,  // 目标节点
               int min_height,                   // 最小飞行高度
               const std::string &agent_id,      // 飞行器ID
               long long path_start_time)        // 路径开始时间
{
    // 从节点中提取位置坐标
    Point3D start_pos = _node_to_point3d(start_node_ref);
    Point3D goal_pos = _node_to_point3d(goal_node_ref);

    // 声明变量，用于存储起飞阶段的路径和最后一个节点
    std::vector<GridNode3D> takeoff_path;
    GridNode3D last_takeoff_node(0, 0, 0); // 初始化为默认值，将在后面设置
    long long cruise_start_time = 0;

    // 1. 垂直起飞阶段 - 通过if-else区分不同的起飞情况
    if (std::get<2>(start_pos) >= min_height)
    {
        // 情况1: 起始高度已经满足最小高度要求，不需要垂直起飞
        // 在当前位置和时间创建起始节点
        last_takeoff_node = GridNode3D(
            std::get<0>(start_pos),
            std::get<1>(start_pos),
            std::get<2>(start_pos),
            path_start_time);
        takeoff_path = {last_takeoff_node};
        cruise_start_time = path_start_time;
    }
    else
    {
        // 情况2: 需要垂直起飞
        auto takeoff_result = _vertical_takeoff(start_pos, min_height, agent_id, path_start_time);
        if (!std::get<0>(takeoff_result))
        {
            return {std::nullopt, std::nullopt, "Takeoff failed: " + std::get<1>(takeoff_result).value_or("Unknown error")};
        }

        takeoff_path = std::get<0>(takeoff_result).value();
        if (takeoff_path.empty())
        {
            return {std::nullopt, std::nullopt, "Takeoff path is empty."};
        }

        last_takeoff_node = takeoff_path.back();
        cruise_start_time = last_takeoff_node.t;
    }

    // 2. 巡航阶段 - 共用代码
    auto cruise_result = _find_cruise_path(
        last_takeoff_node,
        {std::get<0>(goal_pos), std::get<1>(goal_pos), min_height}, // 在最小高度巡航
        min_height,
        agent_id,
        cruise_start_time);

    // 处理巡航结果
    if (!std::get<0>(cruise_result))
    {
        return {std::nullopt, std::nullopt, "Cruise failed: " + std::get<1>(cruise_result).value_or("Unknown error")};
    }

    // 获取巡航路径并继续降落阶段
    std::vector<GridNode3D> cruise_path = std::get<0>(cruise_result).value();

    // 3. 垂直降落阶段
    auto landing_result = _vertical_landing(
        cruise_path.back(),
        goal_pos,
        agent_id);

    if (!std::get<0>(landing_result))
    {
        return {std::nullopt, std::nullopt, "Landing failed: " + std::get<1>(landing_result).value_or("Unknown error")};
    }

    std::vector<GridNode3D> landing_path = std::get<0>(landing_result).value();

    // 确定巡航路径的转弯点
    std::vector<GridNode3D> cruise_turn_path;
    std::vector<int> cruise_turn_indices;

    // 从巡航路径中提取转弯点
    auto [turn_path, turn_indices] = extract_turning_points(cruise_path);
    cruise_turn_path = turn_path;
    cruise_turn_indices = turn_indices;

    // 如果需要，应用平滑处理
    if (need_smooth_)
    {
        auto [smoothed_path, smoothed_turn_path] = moving_average_smooth(
            cruise_path,
            cruise_turn_indices,
            min_height,
            agent_id);

        cruise_path = smoothed_path;
        cruise_turn_path = smoothed_turn_path;
    }

    // 合并所有路径段
    std::vector<GridNode3D> complete_path;
    complete_path.insert(complete_path.end(), takeoff_path.begin(), takeoff_path.end());

    // 如果巡航路径的第一个节点与起飞路径的最后一个节点相同，则跳过
    auto cruise_start = cruise_path.begin();
    if (!takeoff_path.empty() && !cruise_path.empty() &&
        takeoff_path.back().x == cruise_path.front().x &&
        takeoff_path.back().y == cruise_path.front().y &&
        takeoff_path.back().z == cruise_path.front().z)
    {
        cruise_start++;
    }

    complete_path.insert(complete_path.end(), cruise_start, cruise_path.end());

    // 如果降落路径的第一个节点与巡航路径的最后一个节点相同，则跳过
    auto landing_start = landing_path.begin();
    if (!cruise_path.empty() && !landing_path.empty() &&
        cruise_path.back().x == landing_path.front().x &&
        cruise_path.back().y == landing_path.front().y &&
        cruise_path.back().z == landing_path.front().z)
    {
        landing_start++;
    }

    complete_path.insert(complete_path.end(), landing_start, landing_path.end());

    // 创建完整的转弯点路径
    std::vector<GridNode3D> complete_turn_path;
    complete_turn_path.push_back(takeoff_path.front()); // 起始点

    // 添加巡航转弯点（如果第一个与起飞终点相同则跳过）
    auto cruise_turn_start = cruise_turn_path.begin();
    if (!takeoff_path.empty() && !cruise_turn_path.empty() &&
        takeoff_path.back().x == cruise_turn_path.front().x &&
        takeoff_path.back().y == cruise_turn_path.front().y &&
        takeoff_path.back().z == cruise_turn_path.front().z)
    {
        cruise_turn_start++;
    }

    complete_turn_path.insert(complete_turn_path.end(), cruise_turn_start, cruise_turn_path.end());

    // 如果降落终点尚未包含，则添加
    if (!complete_turn_path.empty() && !landing_path.empty() &&
        !(complete_turn_path.back().x == landing_path.back().x &&
          complete_turn_path.back().y == landing_path.back().y &&
          complete_turn_path.back().z == landing_path.back().z))
    {
        complete_turn_path.push_back(landing_path.back());
    }

    return {complete_path, complete_turn_path, std::nullopt};
}

// --- 其他私有方法实现（已存在或待实现的存根）---

std::pair<bool, std::optional<std::string>> JPS::_check_constraints_and_collisions(
    const Point3D &pos, // x, y, z
    long long time,
    const std::string &agent_id)
// const std::vector<Constraint>& constraints)
{
    if (!map_->is_traversable(pos)) // is_traversable should handle boundary checks
    {
        std::vector<std::string> actual_obstacle_types;
        // 假定ObstacleManager可以提供某点的障碍类型，或从is_traversable推断
        // 简单起见，如果不可通行，就视为障碍物或超出边界
        std::ostringstream oss;
        oss << "Position (" << std::get<0>(pos) << "," << std::get<1>(pos) << "," << std::get<2>(pos) << ") "
            << "is not traversable (obstacle or out of bounds).";
        // TODO: Get specific obstacle types if possible from map_->get_obstacle_manager()
        return {false, oss.str()};
    }

    if (occupancy_map_)
    {
        if (occupancy_map_->check_collision(pos, time)) // Assuming this checks against other agents
        {
            // Python版本的OccupancyMap.check_collision返回(bool, colliding_agent_id)
            // 且JPS检查`if has_collision and colliding_agent != agent_id:`
            // 如果C++版本的OccupancyMap::check_collision不知道当前agent_id来排除自碰撞，
            // 可能需要调整或OccupancyMap需要agent_id参数
            // 目前假设check_collision已处理此问题或这是一般的动态障碍物
            std::ostringstream oss;
            oss << "Dynamic collision detected at (" << std::get<0>(pos) << "," << std::get<1>(pos) << "," << std::get<2>(pos)
                << ") at time " << time;
            return {false, oss.str()};
        }
    }
    // 实际约束检查的占位符
    // if (constraints) { ... }
    return {true, std::nullopt};
}

std::pair<bool, std::optional<std::string>> JPS::_is_valid_position(
    const Point3D &pos_xyz, // x, y, z
    long long time,
    int min_height_z,
    const std::string &agent_id,
    // const std::vector<Constraint>& constraints,
    bool ignore_min_height)
{
    // 边界检查通过map_->is_traversable在_check_constraints_and_collisions中隐式处理
    // Python中: pos[0]是y, pos[1]是x. 这里Point3D是(x,y,z)
    // int cpp_x = std::get<0>(pos_xyz);
    // int cpp_y = std::get<1>(pos_xyz);
    int cpp_z = std::get<2>(pos_xyz);

    // 这里不需要显式边界检查，因为map_->is_traversable（通过_check_constraints_and_collisions调用）会处理它
    // 如果map_->is_traversable返回false，_check_constraints_and_collisions会返回错误

    auto base_check = _check_constraints_and_collisions(pos_xyz, time, agent_id); //, constraints);
    if (!base_check.first)
    {
        return base_check;
    }

    if (!ignore_min_height && cpp_z < min_height_z)
    {
        std::ostringstream oss;
        // Assuming GridConverter is accessible for geo conversion if needed for error message
        // GeoCoordinate real_alt_geo = map_->get_converter().grid_to_geographic(0,0,static_cast<float>(cpp_z));
        // GeoCoordinate min_alt_geo = map_->get_converter().grid_to_geographic(0,0,static_cast<float>(min_height_z));
        oss << "Altitude " << cpp_z << " is below minimum cruise altitude " << min_height_z;
        return {false, oss.str()};
    }

    return {true, std::nullopt};
}

double JPS::_octile_distance(const Point3D &p1_xyz, const Point3D &p2_xyz) const
{
    int dx_abs = std::abs(std::get<0>(p1_xyz) - std::get<0>(p2_xyz)); // dx for x-coordinate
    int dy_abs = std::abs(std::get<1>(p1_xyz) - std::get<1>(p2_xyz)); // dy for y-coordinate
    int dz_abs = std::abs(std::get<2>(p1_xyz) - std::get<2>(p2_xyz)); // dz for z-coordinate

    const double D_diag = std::sqrt(2.0); // Cost for diagonal in 2D plane (XY)
    // Octile for XY plane, then add Z movement cost
    double cost_xy = D_diag * static_cast<double>(std::min(dx_abs, dy_abs)) +
                     static_cast<double>(std::abs(dx_abs - dy_abs));
    double cost_z = static_cast<double>(dz_abs); // Z轴移动每个网格单位的代价为1

    return cost_xy + cost_z;
}

// _get_neighbors 实现 - 获取当前节点的邻居节点
// 考虑水平和垂直方向的移动，以及垂直避障
// 坐标系转换：Python的(y, x, z)对应C++的(x, y, z)
std::vector<JPS::NeighborPath> JPS::_get_neighbors(
    const GridNode3D &current_node, // 当前节点
    int min_height,                 // 最小飞行高度
    const std::string &agent_id)    // 飞行器ID
// const std::vector<Constraint>& constraints,
// int vertical_look_steps)
{
    std::vector<NeighborPath> valid_neighbors;
    std::vector<Direction3D> blocked_directions;

    Point3D current_pos = _node_to_point3d(current_node);
    int curr_x = std::get<0>(current_pos);
    int curr_y = std::get<1>(current_pos);
    int curr_z = std::get<2>(current_pos);

    // 首先检查水平方向（XY平面）
    for (const auto &dir : DIRECTION_PRIORITIES)
    {
        // 虽然存储为(dx,dy,dz)，但为了清晰，我们将其视为(dy,dx,dz)
        int dy = std::get<0>(dir); // 在代码中是dx，但概念上是dy
        int dx = std::get<1>(dir); // 在代码中是dy，但概念上是dx
        int dz = std::get<2>(dir);

        // 首先只处理水平方向
        if (dz != 0)
            continue;

        // 计算使用跳跃步长的新位置
        int jump_step = current_node.jump_step;
        int nx = curr_x + dx * jump_step; // dx实际上是水平方向的变化
        int ny = curr_y + dy * jump_step; // dy实际上是垂直方向的变化
        int nz = curr_z;                  // 保持相同的z层级

        // 检查新位置是否在地图边界内
        if (nx < 0 || nx >= width_ || ny < 0 || ny >= height_ || nz < 0 || nz >= depth_)
        {
            continue;
        }

        // 如果新位置是父节点则跳过
        if (current_node.parent != nullptr)
        {
            Point3D parent_pos = _node_to_point3d(*current_node.parent);
            if (nx == std::get<0>(parent_pos) &&
                ny == std::get<1>(parent_pos) &&
                nz == std::get<2>(parent_pos))
            {
                continue;
            }
        }

        Point3D new_pos = {nx, ny, nz};

        // 检查位置是否可通行
        if (map_->is_traversable(new_pos))
        {
            // Create a path with just this point
            NeighborPath path;
            path.push_back(new_pos);
            valid_neighbors.push_back(path);
        }
        else
        {
            // 如果被阻挡，记录该方向用于垂直绕行
            Direction3D blocked_dir = {dy, dx, 0}; // 注意：存储为(dy,dx,dz)，但概念上是(dy,dx,dz)
            blocked_directions.push_back(blocked_dir);
        }
    }

    // 对被阻挡的水平方向，尝试垂直绕行
    for (const auto &blocked_dir : blocked_directions)
    {
        int dy = std::get<0>(blocked_dir); // 在代码中是dx，但概念上是dy
        int dx = std::get<1>(blocked_dir); // 在代码中是dy，但概念上是dx

        // Try vertical steps (up to default_jump_step_size_)
        for (int step = 1; step <= default_jump_step_size_; step += 2)
        {
            // 首先检查是否可以从当前位置向上移动
            int up_z = curr_z + step;

            // 检查向上的位置是否在边界内
            if (up_z >= depth_)
            {
                break; // 无法继续向上
            }

            // 检查是否可以向上移动
            Point3D up_pos = {curr_x, curr_y, up_z};
            if (!map_->is_traversable(up_pos))
            {
                continue; // 在这一步无法向上移动
            }

            // 现在检查是否可以在较高层级水平移动
            int nx = curr_x + dx * current_node.jump_step;
            int ny = curr_y + dy * current_node.jump_step;

            // 检查新位置是否在边界内
            if (nx < 0 || nx >= width_ || ny < 0 || ny >= height_)
            {
                break; // 超出边界
            }

            Point3D new_pos = {nx, ny, up_z};

            // 检查较高位置是否可通行
            if (map_->is_traversable(new_pos))
            {
                // Create a path with intermediate points
                NeighborPath path;
                path.push_back(up_pos);  // First go up
                path.push_back(new_pos); // Then go horizontally
                valid_neighbors.push_back(path);
                break; // 找到有效高度，无需继续向上
            }
        }
    }

    // 添加垂直方向（向上和向下）
    for (const auto &dir : DIRECTION_PRIORITIES)
    {
        int dy = std::get<0>(dir); // 在代码中是dx，但概念上是dy
        int dx = std::get<1>(dir); // 在代码中是dy，但概念上是dx
        int dz = std::get<2>(dir);

        // 只处理垂直方向
        if (dy != 0 || dx != 0 || dz == 0)
            continue;

        // Calculate new position
        int nz = curr_z + dz;

        // 检查新位置是否在地图边界内
        if (nz < 0 || nz >= depth_)
        {
            continue;
        }

        Point3D new_pos = {curr_x, curr_y, nz};

        // 检查位置是否可通行
        if (map_->is_traversable(new_pos))
        {
            // 创建仅包含此点的路径
            NeighborPath path;
            path.push_back(new_pos);
            valid_neighbors.push_back(path);
        }
    }

    // 根据需要基于动态约束过滤邻居节点
    if (occupancy_map_ != nullptr)
    {
        std::vector<NeighborPath> filtered_neighbors;

        for (const auto &path : valid_neighbors)
        {
            bool path_valid = true;

            for (const auto &pos : path)
            {
                // 计算到达该位置所需的时间
                long long next_time = current_node.t + static_cast<long long>(std::round(cruise_speed_t_));

                // 检查位置是否满足约束条件
                auto validity = _is_valid_position(pos, next_time, min_height, agent_id);
                if (!validity.first)
                {
                    path_valid = false;
                    break;
                }
            }

            if (path_valid)
            {
                filtered_neighbors.push_back(path);
            }
        }

        return filtered_neighbors;
    }

    return valid_neighbors;
}

// _jump 函数实现 - 跳点搜索的核心函数
// 从当前节点沿指定方向搜索，直到找到跳点或无法继续
// 跳点定义：1. 目标点 2. 有强制邻居的点 3. 起始点
std::optional<GridNode3D> JPS::_jump(
    const GridNode3D &current_node, // 当前节点
    const GridNode3D &parent_node,  // 父节点（用于确定搜索方向）
    const Point3D &goal_pos,        // 目标位置
    int min_height,                 // 最小飞行高度
    const std::string &agent_id,    // 飞行器ID
    int jump_step)                  // 跳跃步长
// const std::vector<Constraint>& constraints,
// int vertical_look_steps)
{
    // 空值检查
    if (current_node.x < 0 || current_node.y < 0 || current_node.z < 0)
    {
        return std::nullopt;
    }

    // 快速目标检查 - 检查是否在XY平面上到达目标
    Point3D current_pos = _node_to_point3d(current_node);
    if (std::get<0>(current_pos) == std::get<0>(goal_pos) &&
        std::get<1>(current_pos) == std::get<1>(goal_pos))
    {
        return current_node; // 在XY平面上找到目标
    }

    // 如果这是起始节点（没有父节点），将其作为跳点返回
    if (parent_node.x < 0 || parent_node.y < 0 || parent_node.z < 0)
    {
        return current_node;
    }

    // 计算从父节点到当前节点的方向
    int dy = 0, dx = 0, dz = 0; // 注意：虽然存储为dx,dy,dz，但概念上是dy,dx,dz

    if (current_node.x != parent_node.x)
    {
        dx = (current_node.x - parent_node.x) / std::abs(current_node.x - parent_node.x) * jump_step; // 水平方向变化
    }

    if (current_node.y != parent_node.y)
    {
        dy = (current_node.y - parent_node.y) / std::abs(current_node.y - parent_node.y) * jump_step; // 垂直方向变化
    }

    if (current_node.z != parent_node.z)
    {
        dz = (current_node.z - parent_node.z) / std::abs(current_node.z - parent_node.z); // 高度变化
    }

    // 计算此步骤的代价
    double per_path_cost = 1.0 * jump_step;
    if (dx != 0 && dy != 0 && dz == 0)
    {
        // XY平面上的对角线移动
        per_path_cost = 1.414 * jump_step; // √2 * jump_step
    }

    // 初始化路径和代价
    std::vector<GridNode3D> intermediate_path;
    intermediate_path.push_back(current_node);

    // 计算下一个位置
    int next_x = current_node.x + dx;
    int next_y = current_node.y + dy;
    int next_z = current_node.z + dz;

    // 计算下一个时间点和代价
    long long next_t = current_node.t + static_cast<long long>(std::round(cruise_speed_t_ * jump_step));
    double next_g = current_node.g + per_path_cost;

    // 创建下一个节点
    GridNode3D next_node(next_x, next_y, next_z, next_t, next_g);
    next_node.jump_step = jump_step;
    next_node.parent = const_cast<GridNode3D *>(&current_node); // 注意：这通常是不安全的

    // 主跳跃循环
    int steps = jump_step;
    while (true)
    {
        // 检查下一个位置是否有效
        Point3D next_pos = _node_to_point3d(next_node);
        auto validity = _is_valid_position(next_pos, next_t, min_height, agent_id);

        // If next position is not valid, try vertical evasion
        if (!validity.first && dz == 0)
        { // Only try vertical evasion for horizontal moves
            // Check if out of bounds
            if (validity.second && validity.second.value().find("out of bounds") != std::string::npos)
            {
                GridNode3D result = current_node;
                result.need_sight = (steps == 0) ? false : true;
                return result;
            }

            // Check if it's a fixed obstacle
            // For simplicity, we'll assume any invalid position is an obstacle
            // In a real implementation, you'd check the specific obstacle type
            GridNode3D result = current_node;
            result.need_sight = false;
            return result;

            // Vertical evasion would be implemented here
            // This is complex and would involve checking multiple vertical steps
            // Similar to the Python implementation's jump_step_size logic
        }

        // If next position is valid, add to path
        if (validity.first)
        {
            intermediate_path.push_back(next_node);

            // Check if we've reached the goal in XY plane
            if (next_x == std::get<0>(goal_pos) && next_y == std::get<1>(goal_pos))
            {
                return next_node;
            }

            // 检查强制邻居（JPS的关键部分）
            // 这是一个简化的检查 - 完整实现会更复杂
            Direction3D direction = {dy / jump_step, dx / jump_step, dz}; // 注意：虽然存储为(dx,dy,dz)，但概念上是(dy,dx,dz)

            // 检查此方向是否有强制邻居检查
            auto forced_iter = FORCED_NEIGHBOR_DIRS.find(direction);
            if (forced_iter != FORCED_NEIGHBOR_DIRS.end())
            {
                // 检查每个潜在的强制邻居方向
                for (const auto &check_dir : forced_iter->second)
                {
                    // 计算要检查的位置
                    Point3D check_pos = {
                        next_x + std::get<0>(check_dir),  // dx方向的偏移
                        next_y + std::get<1>(check_dir),  // dy方向的偏移
                        next_z + std::get<2>(check_dir)}; // dz方向的偏移

                    // 检查位置是否有效
                    auto check_validity = _is_valid_position(
                        check_pos,
                        next_t + static_cast<long long>(std::round(cruise_speed_t_)),
                        min_height,
                        agent_id);

                    // 如果有效，这是一个强制邻居 - 将当前节点作为跳点返回
                    if (check_validity.first)
                    {
                        return next_node;
                    }
                }
            }

            // Create a copy of next_node to use as the new current_node
            GridNode3D current_node_copy = next_node;

            // Update next position based on the new current node
            next_x = current_node_copy.x + dx;
            next_y = current_node_copy.y + dy;
            next_z = current_node_copy.z + dz;

            // Update next time and cost
            next_t = current_node_copy.t + static_cast<long long>(std::round(cruise_speed_t_ * jump_step));
            next_g = current_node_copy.g + per_path_cost;

            // Create next node
            next_node = GridNode3D(next_x, next_y, next_z, next_t, next_g);
            next_node.jump_step = jump_step;
            next_node.parent = new GridNode3D(current_node_copy); // Create a new node on the heap

            steps += jump_step;

            // Check if we're still moving toward the goal
            double current_dist = _octile_distance(_node_to_point3d(current_node_copy), goal_pos);
            double next_dist = _octile_distance({next_x, next_y, next_z}, goal_pos);

            if (current_dist - next_dist <= 0 || steps > max_steps_)
            {
                return current_node_copy;
            }
        }
        else
        {
            // If next position is not valid and we couldn't do vertical evasion
            return current_node;
        }
    }

    // Should never reach here
    return std::nullopt;
}

// Heuristic function (as defined in JPS.h, needs implementation if not already)
double JPS::_heuristic(const Point3D &p, const Point3D &goal) const
{
    return heuristic_weight_ * _octile_distance(p, goal);
}

double JPS::_euclidean_distance_squared(const Point3D &p1, const Point3D &p2) const
{
    double dx = static_cast<double>(std::get<0>(p1) - std::get<0>(p2));
    double dy = static_cast<double>(std::get<1>(p1) - std::get<1>(p2));
    double dz = static_cast<double>(std::get<2>(p1) - std::get<2>(p2));
    return dx * dx + dy * dy + dz * dz;
}

// has_line_of_sight 实现 - 检查两点之间是否有直接视线
// 使用Bresenham算法的3D变体来检查两点之间的路径是否可行
std::pair<bool, std::vector<GridNode3D>> JPS::has_line_of_sight(
    const GridNode3D &start_node_los, // 起始节点
    const Point3D &end_pos_los,       // 终点位置
    long long los_start_time,         // 开始时间
    int min_height,                   // 最小飞行高度
    const std::string &agent_id)      // 飞行器ID
// const std::vector<Constraint>& constraints)
{
    // 如果点非常接近，无需进行视线检查
    Point3D start_pos = _node_to_point3d(start_node_los);
    double dist_squared = _euclidean_distance_squared(start_pos, end_pos_los);
    if (dist_squared < 25.0)
    { // 阈值为5^2
        std::vector<GridNode3D> path;
        path.push_back(start_node_los);
        return {true, path};
    }

    // 计算方向向量和步数
    int start_x = std::get<0>(start_pos);
    int start_y = std::get<1>(start_pos);
    int start_z = std::get<2>(start_pos);

    int end_x = std::get<0>(end_pos_los);
    int end_y = std::get<1>(end_pos_los);
    int end_z = std::get<2>(end_pos_los);

    int dx = end_x - start_x;
    int dy = end_y - start_y;
    int dz = end_z - start_z;

    int steps = std::max({std::abs(dx), std::abs(dy), std::abs(dz)});

    if (steps == 0)
    {
        std::vector<GridNode3D> path;
        path.push_back(start_node_los);
        return {true, path};
    }

    // 计算每步的增量
    double sx = static_cast<double>(dx) / steps;
    double sy = static_cast<double>(dy) / steps;
    double sz = static_cast<double>(dz) / steps;

    // 检查路径上的每个点
    long long current_t = los_start_time;
    std::vector<GridNode3D> path_points;

    GridNode3D *parent_node = const_cast<GridNode3D *>(&start_node_los);
    GridNode3D current_node = start_node_los;
    path_points.push_back(current_node);

    Point3D last_pos = start_pos; // 记录上一个位置以避免重复

    for (int i = 1; i <= steps; i += start_node_los.jump_step)
    {
        // 计算当前点坐标（四舍五入到最近的网格点）
        double y_float = start_y + sy * i;
        double x_float = start_x + sx * i;
        double z_float = start_z + sz * i;

        int y = static_cast<int>(y_float + 0.5);
        int x = static_cast<int>(x_float + 0.5);
        int z = static_cast<int>(z_float + 0.5);

        Point3D current_pos = {x, y, z};

        // 如果当前点与上一个点相同，则跳过
        if (current_pos == last_pos)
        {
            continue;
        }

        // 更新时间（仅当点发生变化时）
        current_t += static_cast<long long>(std::round(cruise_speed_t_));

        // 检查位置是否有效
        auto validity = _is_valid_position(current_pos, current_t, min_height, agent_id);
        if (!validity.first)
        {
            // 返回false和最后一个有效节点
            return {false, {current_node}};
        }

        // 记录有效点及其时间
        current_node = GridNode3D(x, y, z, current_t);
        current_node.parent = parent_node;
        path_points.push_back(current_node);

        parent_node = &path_points.back();
        last_pos = current_pos;
    }

    // 如果终点不是最后一个点，则添加终点
    if (last_pos != end_pos_los)
    {
        current_t += static_cast<long long>(std::round(cruise_speed_t_));
        GridNode3D end_node(std::get<0>(end_pos_los), std::get<1>(end_pos_los), std::get<2>(end_pos_los), current_t);
        end_node.parent = parent_node;
        path_points.push_back(end_node);
    }

    return {true, path_points};
}

// extract_turning_points 实现 - 从路径中提取转弯点
// 通过检查相邻路径段的方向变化来识别转弯点
std::pair<std::vector<GridNode3D>, std::vector<int>> JPS::extract_turning_points(
    const std::vector<GridNode3D> &path) // 输入路径
{
    if (path.empty() || path.size() <= 2)
    {
        return {path, {}}; // 如果路径为空或只有2个点，返回原始路径和空索引
    }

    // 初始化转弯点列表和索引列表
    std::vector<GridNode3D> turning_points;
    std::vector<int> turning_indices;

    // 始终包含起始点
    turning_points.push_back(path[0]);

    // 检查每个点的方向变化
    for (size_t i = 1; i < path.size() - 1; ++i)
    {
        const GridNode3D &prev_node = path[i - 1];
        const GridNode3D &curr_node = path[i];
        const GridNode3D &next_node = path[i + 1];

        // 计算方向向量
        double curr_dir_x = curr_node.x - prev_node.x;
        double curr_dir_y = curr_node.y - prev_node.y;
        double curr_dir_z = curr_node.z - prev_node.z;

        double curr_norm = std::sqrt(curr_dir_x * curr_dir_x + curr_dir_y * curr_dir_y + curr_dir_z * curr_dir_z);
        if (curr_norm < 0.001)
            continue; // 如果线段长度为零则跳过

        // 归一化当前方向向量
        curr_dir_x /= curr_norm;
        curr_dir_y /= curr_norm;
        curr_dir_z /= curr_norm;

        // 计算下一个方向向量
        double next_dir_x = next_node.x - curr_node.x;
        double next_dir_y = next_node.y - curr_node.y;
        double next_dir_z = next_node.z - curr_node.z;

        double next_norm = std::sqrt(next_dir_x * next_dir_x + next_dir_y * next_dir_y + next_dir_z * next_dir_z);
        if (next_norm < 0.001)
            continue; // 如果线段长度为零则跳过

        // 归一化下一个方向向量
        next_dir_x /= next_norm;
        next_dir_y /= next_norm;
        next_dir_z /= next_norm;

        // 检查方向是否改变
        bool direction_changed = false;

        // 检查方向向量的任何分量是否发生变化
        if (std::abs(curr_dir_x - next_dir_x) > 0.001 ||
            std::abs(curr_dir_y - next_dir_y) > 0.001 ||
            std::abs(curr_dir_z - next_dir_z) > 0.001)
        {

            // 计算方向向量之间的平方距离，检查变化是否显著
            double dist_squared = (curr_dir_x - next_dir_x) * (curr_dir_x - next_dir_x) +
                                  (curr_dir_y - next_dir_y) * (curr_dir_y - next_dir_y) +
                                  (curr_dir_z - next_dir_z) * (curr_dir_z - next_dir_z);

            // 使用小阈值确定方向变化是否显著
            if (dist_squared > 0.02)
            {
                direction_changed = true;
            }
        }

        // 如果方向改变，将当前点添加为转弯点
        if (direction_changed)
        {
            turning_points.push_back(curr_node);
            turning_indices.push_back(static_cast<int>(i)); // 记录索引
        }
    }

    // 始终包含终点
    turning_points.push_back(path.back());

    return {turning_points, turning_indices};
}

// moving_average_smooth 实现 - 使用移动平均法平滑路径
// 对路径中的点应用加权移动平均，同时保持转弯点的特性
std::pair<std::vector<GridNode3D>, std::vector<GridNode3D>> JPS::moving_average_smooth(
    const std::vector<GridNode3D> &path,     // 输入路径
    const std::vector<int> &turning_indices, // 转弯点索引
    int min_height,                          // 最小飞行高度
    const std::string &agent_id)             // 飞行器ID
// const std::vector<Constraint>& constraints)
{
    if (path.size() < 3)
    {
        return {path, path}; // 点数不足，无法平滑
    }

    // 根据平滑参数计算窗口大小
    int window_size = std::max(3, static_cast<int>(smoothness_));
    if (window_size % 2 == 0)
    {
        window_size += 1; // 确保窗口大小为奇数
    }

    int half_window = window_size / 2;

    // 创建要平滑的索引集合（围绕转弯点）
    std::set<int> indices_to_smooth;

    // 标记每个转弯点周围的点进行平滑处理
    for (int turn_idx : turning_indices)
    {
        int start_idx = std::max(1, turn_idx - half_window);
        int end_idx = std::min(static_cast<int>(path.size()) - 2, turn_idx + half_window);

        for (int idx = start_idx; idx <= end_idx; ++idx)
        {
            indices_to_smooth.insert(idx);
        }
    }

    // 如果没有要平滑的点，返回原始路径
    if (indices_to_smooth.empty())
    {
        // 创建转弯路径（起点、转弯点、终点）
        std::vector<GridNode3D> turning_path;
        turning_path.push_back(path.front()); // 起点

        for (int idx : turning_indices)
        {
            turning_path.push_back(path[idx]);
        }

        turning_path.push_back(path.back()); // 终点
        return {path, turning_path};
    }

    // 初始化结果列表
    std::vector<GridNode3D> smoothed_path;
    smoothed_path.push_back(path.front()); // 保持起点不变

    // 创建平滑的转弯路径，从起点开始
    std::vector<GridNode3D> smoothed_turning_path;
    smoothed_turning_path.push_back(path.front());

    // 创建转弯索引集合，用于快速查找
    std::set<int> turning_indices_set(turning_indices.begin(), turning_indices.end());

    // 处理中间点
    for (size_t i = 1; i < path.size() - 1; ++i)
    {
        const GridNode3D &curr_node = path[i];
        bool is_turning_point = (turning_indices_set.find(static_cast<int>(i)) != turning_indices_set.end());

        // 如果不在平滑集合中，保持原始点
        if (indices_to_smooth.find(static_cast<int>(i)) == indices_to_smooth.end())
        {
            smoothed_path.push_back(curr_node);

            // 如果是转弯点，添加到转弯路径
            if (is_turning_point)
            {
                smoothed_turning_path.push_back(curr_node);
            }
            continue;
        }

        // 检查当前点是否涉及高度变化
        const GridNode3D &prev_node = path[i - 1];
        const GridNode3D &next_node = path[i + 1];

        if (curr_node.z != prev_node.z || curr_node.z != next_node.z)
        {
            // 不平滑高度变化的点
            smoothed_path.push_back(curr_node);

            // 如果是转弯点，添加到转弯路径
            if (is_turning_point)
            {
                smoothed_turning_path.push_back(curr_node);
            }
            continue;
        }

        // 确定平滑的窗口范围
        int left_available = static_cast<int>(i);
        int right_available = static_cast<int>(path.size() - 1 - i);
        int actual_half_window = std::min({half_window, left_available, right_available});

        size_t start_idx = i - actual_half_window;
        size_t end_idx = i + actual_half_window;

        // 确保窗口至少有一个点
        if (start_idx >= end_idx)
        {
            smoothed_path.push_back(curr_node);
            if (is_turning_point)
            {
                smoothed_turning_path.push_back(curr_node);
            }
            continue;
        }

        // 计算加权平均的权重
        std::vector<double> weights(end_idx - start_idx + 1);
        std::vector<const GridNode3D *> window_nodes;

        // 创建节点窗口并计算权重
        for (size_t w_idx = start_idx; w_idx <= end_idx; ++w_idx)
        {
            window_nodes.push_back(&path[w_idx]);

            // 基于与中心距离计算高斯权重
            double x = 2.0 * (static_cast<double>(w_idx) - static_cast<double>(i)) /
                       static_cast<double>(actual_half_window);
            weights[w_idx - start_idx] = std::exp(-0.5 * x * x);
        }

        // 归一化权重
        double sum_weights = 0.0;
        for (double w : weights)
        {
            sum_weights += w;
        }

        for (double &w : weights)
        {
            w /= sum_weights;
        }

        // 计算加权平均
        double x_sum = 0.0, y_sum = 0.0;
        for (size_t w_idx = 0; w_idx < window_nodes.size(); ++w_idx)
        {
            x_sum += window_nodes[w_idx]->x * weights[w_idx];
            y_sum += window_nodes[w_idx]->y * weights[w_idx];
        }

        // 四舍五入到最近的整数
        int smoothed_x = static_cast<int>(std::round(x_sum));
        int smoothed_y = static_cast<int>(std::round(y_sum));
        int original_z = curr_node.z; // 保持原始Z坐标

        // 检查平滑后的位置是否有效
        Point3D smoothed_pos = {smoothed_x, smoothed_y, original_z};
        auto validity = _is_valid_position(smoothed_pos, curr_node.t, min_height, agent_id);

        if (!validity.first)
        {
            // 如果无效，使用原始点
            smoothed_path.push_back(curr_node);
            if (is_turning_point)
            {
                smoothed_turning_path.push_back(curr_node);
            }
            continue;
        }

        // 创建新的平滑节点
        GridNode3D smoothed_node(smoothed_x, smoothed_y, original_z, curr_node.t);
        smoothed_node.g = curr_node.g;
        smoothed_node.h = curr_node.h;
        smoothed_node.f = curr_node.f;
        smoothed_node.jump_step = curr_node.jump_step;
        smoothed_node.need_sight = curr_node.need_sight;

        // 添加到平滑路径
        smoothed_path.push_back(smoothed_node);

        // 如果是转弯点，添加到转弯路径
        if (is_turning_point)
        {
            smoothed_turning_path.push_back(smoothed_node);
        }
    }

    // 添加终点
    smoothed_path.push_back(path.back());
    smoothed_turning_path.push_back(path.back());

    return {smoothed_path, smoothed_turning_path};
}

// _find_cruise_path 实现 - 巡航阶段路径规划
// 在固定高度层执行路径搜索，这是整个路径规划中最复杂的阶段
// 使用A*算法的框架，但结合了跳点搜索(JPS)来优化搜索效率
std::tuple<
    std::optional<std::vector<GridNode3D>>, // 路径节点序列
    std::optional<std::string>              // 错误信息
    >
JPS::_find_cruise_path(
    const GridNode3D &start_node, // 起始节点（起飞结束位置）
    const Point3D &goal_pos,      // 目标位置（降落起始位置）
    int min_height,               // 最小飞行高度
    const std::string &agent_id,  // 飞行器ID
    long long cruise_start_time)  // 巡航开始时间
{
    // 检查目标是否可达
    double distance = _euclidean_distance_squared(goal_pos, _node_to_point3d(start_node));
    distance = std::sqrt(distance);
    long long goal_time = cruise_start_time + static_cast<long long>(std::round(distance * cruise_speed_t_));

    auto goal_validity = _is_valid_position(goal_pos, goal_time, min_height, agent_id);
    if (!goal_validity.first)
    {
        return {std::nullopt, "巡航目标被阻挡: " + goal_validity.second.value_or("未知原因")};
    }

    // A*搜索的优先队列
    // 每个条目格式为 {f值, 唯一计数器, 节点}
    using PQEntry = std::tuple<double, int, GridNode3D>;
    std::priority_queue<PQEntry, std::vector<PQEntry>, std::greater<PQEntry>> open_list;

    // 已访问节点集合
    std::set<Point3D> closed;

    // 用于在优先队列中打破平局的计数器
    int counter = 0;

    // 初始化起始节点
    GridNode3D start_node_copy = start_node; // 复制以避免修改原始节点
    start_node_copy.g = 0.0;
    start_node_copy.h = _heuristic(_node_to_point3d(start_node_copy), goal_pos);
    start_node_copy.f = start_node_copy.h;

    // 根据到目标的距离设置跳跃步长
    if (start_node_copy.h < 25.0)
    {
        start_node_copy.jump_step = 1;
    }
    else
    {
        start_node_copy.jump_step = 5; // 默认跳跃步长
    }

    // 将起始节点添加到开放列表
    open_list.push({start_node_copy.f, counter++, start_node_copy});

    // A*搜索主循环
    while (!open_list.empty())
    {
        // 获取f值最小的节点
        GridNode3D current = std::get<2>(open_list.top());
        open_list.pop();

        Point3D current_pos = _node_to_point3d(current);

        // 如果已经探索过，则跳过
        if (closed.find(current_pos) != closed.end())
        {
            continue;
        }

        // 检查是否有到目标的直接视线
        if (current.need_sight)
        {
            auto [has_los, los_points] = has_line_of_sight(
                current, goal_pos, current.t, min_height, agent_id);

            if (has_los && !los_points.empty())
            {
                // 参考Python代码重建路径：从最后一个节点开始，通过parent指针回溯
                std::vector<GridNode3D> path;
                GridNode3D *node = &los_points.back(); // 从最后一个节点开始
                while (node)
                {
                    path.push_back(*node);
                    node = node->parent;
                }
                // 反转路径，使其从起点到终点
                std::reverse(path.begin(), path.end());

                // 返回路径（直线视线情况）
                return {path, std::nullopt};
            }
        }

        // 将当前节点添加到已访问集合
        closed.insert(current_pos);

        // 获取邻居节点
        auto neighbors = _get_neighbors(current, min_height, agent_id);

        // 处理每个邻居路径
        std::vector<std::pair<double, GridNode3D>> next_list;
        for (const auto &neighbor_path : neighbors)
        {
            // 如果路径为空或最后一点已经在关闭集中，则跳过
            if (neighbor_path.empty())
                continue;

            Point3D last_point = neighbor_path.back();
            if (closed.find(last_point) != closed.end())
            {
                continue;
            }

            // 处理路径中的每个点
            GridNode3D *prev_node = &current;
            GridNode3D last_node(0, 0, 0); // 将被覆盖

            for (const auto &point : neighbor_path)
            {
                long long next_t = prev_node->t + static_cast<long long>(std::round(cruise_speed_t_));

                GridNode3D next_node(
                    std::get<0>(point),
                    std::get<1>(point),
                    std::get<2>(point),
                    next_t);

                // 设置父节点并计算代价
                next_node.parent = prev_node;
                next_node.g = prev_node->g + 1.0; // 每步代价为1
                next_node.h = _heuristic(point, goal_pos);
                next_node.f = next_node.g + next_node.h;
                next_node.jump_step = current.jump_step;

                last_node = next_node;
                prev_node = &last_node;
            }

            // 添加到next_list，按h值排序（用于选择最优邻居）
            next_list.push_back({last_node.h, last_node});
        }

        // 按h值对next_list排序
        std::sort(next_list.begin(), next_list.end(),
                  [](const auto &a, const auto &b)
                  { return a.first < b.first; });

        // 处理最优邻居（限制为3个，与Python实现一致）
        int neighbor_count = 0;
        for (const auto &[h_val, next_node] : next_list)
        {
            if (neighbor_count > 2)
                break;
            neighbor_count++;

            // 从该邻居尝试跳点
            auto jump_node_opt = _jump(
                next_node,
                current,
                goal_pos,
                min_height,
                agent_id,
                next_node.jump_step);

            if (!jump_node_opt)
                continue;

            GridNode3D jump_node = jump_node_opt.value();

            // 更新启发式值和f值
            jump_node.h = _heuristic(_node_to_point3d(jump_node), goal_pos);
            jump_node.f = jump_node.g + jump_node.h;

            // 对接近目标的情况调整跳跃步长
            if (jump_node.h < 20.0)
            {
                jump_node.jump_step = 1;
            }

            // 添加到开放列表
            open_list.push({jump_node.f, counter++, jump_node});
        }
    }

    // 如果到达这里，说明没有找到路径
    return {std::nullopt, "No cruise path found"};
}
