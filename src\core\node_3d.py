import math
from collections import defaultdict
from typing import Dict, List, Set, Tuple, Optional
from .map.occupancy_map import OccupancyMap


def item_in_list_3d(l, idx):
    """获取列表中指定索引的项，如果超出范围则返回最后一项的坐标"""
    try:
        return l[idx]
    except IndexError:
        return l[-1]


def list_conflicts_3d(all_locations, t):
    """检查指定时间步的冲突，使用OccupancyMap进行高效检测

    Args:
        all_locations: 每个智能体在t和t-1时刻的位置列表
        t: 当前时间步

    Returns:
        冲突列表，每个冲突为 ('v', (agent1, agent2), (y, x, z, t))
    """
    # 创建临时占用图，不使用时间缓冲区以提高性能
    occ_map = OccupancyMap((100, 100, 100), time_buffer=0)

    # 先添加t-1时刻的位置
    for agent_id, pos in all_locations[0]:  # t-1时刻
        if pos:
            occ_map.add_path([pos], agent_id)

    # 检查t时刻的位置
    conflicts = []
    for agent_id, pos in all_locations[1]:  # t时刻
        if pos:
            has_collision, colliding_agent = occ_map.check_collision(pos, t)
            if has_collision and colliding_agent != agent_id:
                conflicts.append(("v", (agent_id, colliding_agent), pos + (t,)))
            occ_map.add_path([pos], agent_id)

    return conflicts


class GridNode3D:
    """3D网格节点，用于A*搜索"""

    def __init__(
        self, y=-1, x=-1, z=-1, t=0, g=math.inf, h=math.inf, f=None, parent=None
    ):
        self.y = y
        self.x = x
        self.z = z
        self.t = t
        self.g = g
        self.h = h
        self.f = f if f is not None else float("inf")
        self.parent = parent

        self.jump_step = 5

        self.need_sight = True

        # self.is_turn = False  # 是否是拐点

    def __lt__(self, other):
        return self.t < other.t


class CTNode3D:
    """CBS约束树节点"""

    def __init__(
        self,
        constraints,
        solution,
        cost,
        parent=None,
        entry=0,
        map_size=(100, 100, 100),
    ):
        """初始化CT节点

        Args:
            constraints: 约束集合，每个约束为 (agent_id, y, x, z, t)
            solution: 解决方案字典，{agent_id: path}
            cost: 总代价
            parent: 父节点
            entry: 用于在OPEN列表中打破平局
            map_size: 地图大小 (height, width, depth)
        """
        self.constraints = constraints
        self.solution = solution
        self.cost = cost
        self.parent = parent
        self.entry = entry
        self.n_conflicts = None
        self.map_size = map_size
        self._occupancy_map = None  # 延迟初始化占用图

    def __eq__(self, other):
        return self.solution == other.solution

    def __lt__(self, other):
        # 优先级：冲突数 > 代价 > 入队顺序
        self_n = self.count_n_of_conflicts()
        other_n = other.count_n_of_conflicts()
        if self_n != other_n:
            return self_n < other_n
        if self.cost != other.cost:
            return self.cost < other.cost
        return self.entry < other.entry

    @property
    def occupancy_map(self):
        """延迟初始化并缓存占用图"""
        if self._occupancy_map is None:
            self._occupancy_map = OccupancyMap(self.map_size, time_buffer=0)
            # 添加所有路径到占用图
            for agent_id, path in self.solution.items():
                self._occupancy_map.add_path(path, agent_id)
        return self._occupancy_map

    def validate_conflicts(self, use_pc=False):
        """验证路径冲突

        Args:
            use_pc: 是否使用路径约束（暂未实现）

        Returns:
            冲突列表
        """
        conflicts = []
        # 遍历每个智能体的路径
        for agent_id, path in self.solution.items():
            for node in path:
                # 检查是否有冲突
                has_collision, colliding_agent = self.occupancy_map.check_collision(
                    (node.y, node.x, node.z), node.t
                )
                if has_collision and colliding_agent != agent_id:
                    conflicts.append(
                        (
                            "v",
                            (agent_id, colliding_agent),
                            (node.y, node.x, node.z, node.t),
                        )
                    )
                    if not use_pc:  # 如果不使用路径约束，找到第一个冲突就返回
                        return conflicts
        return conflicts

    def count_n_of_conflicts(self):
        """计算冲突数量"""
        if self.n_conflicts is None:
            self.n_conflicts = len(self.validate_conflicts(use_pc=True))
        return self.n_conflicts

    def get_all_constraints(self):
        """获取从根节点到当前节点的所有约束"""
        all_constraints = set()
        node = self
        while node:
            all_constraints.update(node.constraints)
            node = node.parent
        return all_constraints
