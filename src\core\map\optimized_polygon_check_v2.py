import numpy as np

# import time  # 不再需要
from functools import lru_cache
from typing import Optional, List

# Try to import Numba for JIT compilation (optional)
try:
    from numba import jit, prange

    HAS_NUMBA = True
except ImportError:
    HAS_NUMBA = False

    # Create dummy decorators
    def jit(*args, **kwargs):
        def decorator(func):
            return func

        return decorator

    def prange(*args):
        return range(*args)


# Try to import rtree for spatial indexing (optional)
try:
    import rtree

    HAS_RTREE = True
except ImportError:
    HAS_RTREE = False


class PolygonIndex:
    """多边形空间索引，用于加速点在多边形内的检测"""

    def __init__(self, polygons=None):
        """
        初始化多边形索引

        Args:
            polygons: 字典，键为多边形ID，值为多边形顶点数组
        """
        self.polygons = {} if polygons is None else polygons
        self.bounds = {}  # 存储每个多边形的边界框
        self.spatial_index = None

        # 如果提供了多边形，初始化索引
        if polygons:
            self.build_index()

    def add_polygon(self, polygon_id, vertices):
        """
        添加多边形到索引

        Args:
            polygon_id: 多边形ID
            vertices: 多边形顶点数组，每行是[y, x]
        """
        self.polygons[polygon_id] = np.asarray(vertices)
        # 计算边界框
        y_min, y_max = np.min(vertices[:, 0]), np.max(vertices[:, 0])
        x_min, x_max = np.min(vertices[:, 1]), np.max(vertices[:, 1])
        self.bounds[polygon_id] = (y_min, y_max, x_min, x_max)

        # 重建索引
        self.build_index()

    def remove_polygon(self, polygon_id):
        """
        从索引中移除多边形

        Args:
            polygon_id: 要移除的多边形ID
        """
        if polygon_id in self.polygons:
            del self.polygons[polygon_id]
            del self.bounds[polygon_id]
            # 重建索引
            self.build_index()

    def build_index(self):
        """构建空间索引"""
        if not self.polygons:
            self.spatial_index = None
            return

        if HAS_RTREE:
            # 使用R树构建空间索引
            p = rtree.index.Property()
            p.dimension = 2
            self.spatial_index = rtree.index.Index(properties=p)

            for idx, (polygon_id, bounds) in enumerate(self.bounds.items()):
                y_min, y_max, x_min, x_max = bounds
                # R树索引使用(min_x, min_y, max_x, max_y)格式
                self.spatial_index.insert(
                    idx, (x_min, y_min, x_max, y_max), obj=polygon_id
                )

    def query_point(self, point):
        """
        查询点可能在哪些多边形内

        Args:
            point: 要查询的点坐标 [y, x]

        Returns:
            可能包含该点的多边形ID列表
        """
        if not self.polygons:
            return []

        y, x = point

        if HAS_RTREE and self.spatial_index:
            # 使用R树查询
            candidates = list(
                self.spatial_index.intersection((x, y, x, y), objects=True)
            )
            return [item.object for item in candidates]
        else:
            # 使用边界框快速筛选
            candidates = []
            for polygon_id, (y_min, y_max, x_min, x_max) in self.bounds.items():
                if y_min <= y <= y_max and x_min <= x <= x_max:
                    candidates.append(polygon_id)
            return candidates

    def contains_point(self, point):
        """
        检查点是否在任何多边形内

        Args:
            point: 要检查的点坐标 [y, x]

        Returns:
            如果点在任何多边形内，返回该多边形ID；否则返回None
        """
        # 确保点是元组（可哈希）
        if not isinstance(point, tuple):
            point = tuple(map(float, point))

        # 首先使用空间索引筛选候选多边形
        candidates = self.query_point(point)

        if not candidates:
            return None

        # 对每个候选多边形进行精确检查
        point_array = np.array([point])
        for polygon_id in candidates:
            polygon = self.polygons[polygon_id]
            # 使用向量化方法直接检查，避免缓存问题
            if points_in_polygon_vectorized(point_array, polygon)[0]:
                return polygon_id

        return None

    def contains_points_batch(self, points):
        """
        批量检查多个点是否在任何多边形内

        Args:
            points: 要检查的点坐标数组，每行是[y, x]

        Returns:
            与points等长的数组，包含每个点所在的多边形ID（如果不在任何多边形内则为None）
        """
        n_points = len(points)
        results = [None] * n_points

        # 对每个点进行检查
        for i, point in enumerate(points):
            # 确保点是元组（可哈希）
            if not isinstance(point, tuple):
                point = tuple(map(float, point))
            results[i] = self.contains_point(point)

        return results


# 使用LRU缓存优化单点检测
@lru_cache(maxsize=10000)
def point_in_polygon_single(point, polygon):
    """
    检查单个点是否在多边形内（带缓存）

    Args:
        point: 点坐标元组 (y, x)
        polygon: 多边形顶点坐标数组或元组

    Returns:
        布尔值，表示点是否在多边形内
    """
    # 确保点是元组（可哈希，用于缓存）
    if not isinstance(point, tuple):
        point = tuple(point)

    # 确保多边形是元组的元组（可哈希，用于缓存）
    if isinstance(polygon, np.ndarray):
        # 将numpy数组转换为元组的元组
        polygon_tuple = tuple(tuple(vertex) for vertex in polygon)
    else:
        polygon_tuple = polygon

    # 将元组转换回numpy数组进行计算
    polygon_array = np.array(polygon_tuple)
    point_array = np.array([point])

    # 使用向量化方法检查
    return points_in_polygon_vectorized(point_array, polygon_array)[0]


def points_in_polygon_vectorized(points, polygon):
    """
    完全向量化的射线投射算法，检查点是否在多边形内

    Args:
        points: nx2数组，每行是[y, x]
        polygon: mx2数组，每行是[y, x]

    Returns:
        长度为n的布尔数组，表示每个点是否在多边形内
    """
    # 对于非常大的数据集，使用网格优化方法
    if len(points) > 1_000_000:
        return points_in_polygon_grid_based(points, polygon)

    # 获取下一个顶点索引（环绕到第一个顶点）
    n_vertices = len(polygon)
    next_vertices = np.roll(np.arange(n_vertices), -1)

    # 准备用于广播的数组
    # 将点重塑为(n, 1, 2)以便与多边形顶点广播
    points_expanded = points[:, np.newaxis, :]

    # 获取多边形边作为顶点对
    polygon_edges = np.stack(
        (polygon, polygon[next_vertices]), axis=1
    )  # shape: (n_vertices, 2, 2)

    # 检查点是否在射线的同一侧
    # 对于每个点和每条边，检查边是否穿过从点向右的射线
    y_check = (polygon_edges[:, 0, 1] > points_expanded[:, :, 1]) != (
        polygon_edges[:, 1, 1] > points_expanded[:, :, 1]
    )

    # 对于穿过射线的边，计算交点的x坐标
    # 并检查它是否在点的右侧
    where_y_check = np.where(y_check)

    # 初始化结果数组（全为False）
    result = np.zeros(len(points), dtype=bool)

    # 只处理y_check为True的点和边
    if len(where_y_check[0]) > 0:
        point_indices = where_y_check[0]
        edge_indices = where_y_check[1]

        # 获取相关点和边
        relevant_points = points[point_indices]
        relevant_edges_start = polygon[edge_indices]
        relevant_edges_end = polygon[next_vertices[edge_indices]]

        # 计算交点的x坐标
        # 添加一个小的epsilon以避免除以零
        denominator = relevant_edges_end[:, 1] - relevant_edges_start[:, 1]
        # 避免除以零
        safe_denominator = np.where(denominator == 0, np.finfo(float).eps, denominator)

        x_intersect = (relevant_edges_end[:, 0] - relevant_edges_start[:, 0]) * (
            relevant_points[:, 1] - relevant_edges_start[:, 1]
        ) / safe_denominator + relevant_edges_start[:, 0]

        # 检查交点是否在点的右侧
        x_check = relevant_points[:, 0] < x_intersect

        # 计算每个点的交叉次数
        # 使用bincount和point_indices作为权重来计算每个点有多少边穿过射线
        crossings = np.bincount(point_indices[x_check], minlength=len(points))

        # 如果射线穿过奇数条边，则点在多边形内
        result = crossings % 2 == 1

    return result


# 定义Numba加速函数（如果可用）
@jit(nopython=True, parallel=True)
def _point_in_polygon_numba_impl(points, polygon):
    """Numba加速的点在多边形内检查，用于大型数据集"""
    n_points = len(points)
    n_vertices = len(polygon)
    result = np.zeros(n_points, dtype=np.bool_)

    # 对每个点
    for i in prange(n_points):
        inside = False
        x, y = points[i]

        # 射线投射算法
        for j in range(n_vertices):
            j_next = (j + 1) % n_vertices
            yi, xi = polygon[j]
            yj, xj = polygon[j_next]

            # 避免除以零
            if abs(yj - yi) < 1e-10:
                continue

            intersect = ((yi > y) != (yj > y)) and (
                x < (xj - xi) * (y - yi) / (yj - yi) + xi
            )
            if intersect:
                inside = not inside

        result[i] = inside

    return result


# 根据Numba可用性选择适当的实现
def _point_in_polygon_numba(points, polygon):
    if HAS_NUMBA:
        return _point_in_polygon_numba_impl(points, polygon)
    else:
        return points_in_polygon_vectorized_simple(points, polygon)


def points_in_polygon_vectorized_simple(points, polygon):
    """
    替代向量化实现，更简单但对于非常大的数据集可能效率较低

    Args:
        points: nx2数组，每行是[y, x]
        polygon: mx2数组，每行是[y, x]

    Returns:
        长度为n的布尔数组，表示每个点是否在多边形内
    """
    n_points = len(points)
    n_vertices = len(polygon)

    # 获取下一个顶点索引（环绕到第一个顶点）
    j = np.roll(np.arange(n_vertices), -1)

    # 初始化结果数组
    result = np.zeros(n_points, dtype=bool)

    # 对于多边形的每条边
    for k in range(n_vertices):
        # 检查边是否穿过从每个点向右的射线
        # 添加一个小的epsilon以避免除以零
        denominator = polygon[j[k], 1] - polygon[k, 1]
        # 跳过水平边（会导致除以零）
        if denominator == 0:
            continue

        mask = ((polygon[k, 1] > points[:, 1]) != (polygon[j[k], 1] > points[:, 1])) & (
            points[:, 0]
            < (polygon[j[k], 0] - polygon[k, 0])
            * (points[:, 1] - polygon[k, 1])
            / denominator
            + polygon[k, 0]
        )

        # 将结果与掩码进行XOR
        result = np.logical_xor(result, mask)

    return result


def points_in_polygon_grid_based(points, polygon, grid_size=100):
    """
    基于网格的点在多边形内检查优化，用于非常大的数据集

    这种方法将空间划分为网格，并将每个单元格分类为：
    - 完全在多边形内
    - 完全在多边形外
    - 与多边形边界相交

    只有与边界相交的单元格中的点需要详细的点在多边形内检查

    Args:
        points: nx2数组，每行是[y, x]
        polygon: mx2数组，每行是[y, x]
        grid_size: 每个维度的网格单元格数量

    Returns:
        长度为n的布尔数组，表示每个点是否在多边形内
    """
    # 获取多边形的边界框
    y_min, y_max = np.min(polygon[:, 0]), np.max(polygon[:, 0])
    x_min, x_max = np.min(polygon[:, 1]), np.max(polygon[:, 1])

    # 添加一个小的边距以确保包含所有点
    margin = 0.001 * max(y_max - y_min, x_max - x_min)
    y_min -= margin
    y_max += margin
    x_min -= margin
    x_max += margin

    # 创建网格
    y_edges = np.linspace(y_min, y_max, grid_size + 1)
    x_edges = np.linspace(x_min, x_max, grid_size + 1)

    # 初始化结果数组
    result = np.zeros(len(points), dtype=bool)

    # 对于每个网格单元格
    for i in range(grid_size):
        for j in range(grid_size):
            # 获取单元格边界
            cell_y_min, cell_y_max = y_edges[i], y_edges[i + 1]
            cell_x_min, cell_x_max = x_edges[j], x_edges[j + 1]

            # 找出此单元格中的点
            cell_mask = (
                (points[:, 0] >= cell_y_min)
                & (points[:, 0] < cell_y_max)
                & (points[:, 1] >= cell_x_min)
                & (points[:, 1] < cell_x_max)
            )
            cell_points = points[cell_mask]

            if len(cell_points) == 0:
                continue

            # 检查单元格角点是否都在多边形内或都在多边形外
            corners = np.array(
                [
                    [cell_y_min, cell_x_min],
                    [cell_y_min, cell_x_max],
                    [cell_y_max, cell_x_min],
                    [cell_y_max, cell_x_max],
                ]
            )

            # 使用简单的向量化方法检查角点
            corners_inside = points_in_polygon_vectorized_simple(corners, polygon)

            # 如果所有角点都在内部，则单元格中的所有点都在内部
            if np.all(corners_inside):
                result[cell_mask] = True
                continue

            # 如果所有角点都在外部，我们需要检查单元格是否与多边形相交
            # 通过检查任何多边形边是否与单元格边界相交
            if not np.any(corners_inside):
                # 检查单元格是否与多边形相交
                cell_intersects_polygon = False

                # 创建单元格边
                cell_edges = [
                    # 底边
                    [[cell_y_min, cell_x_min], [cell_y_min, cell_x_max]],
                    # 右边
                    [[cell_y_min, cell_x_max], [cell_y_max, cell_x_max]],
                    # 顶边
                    [[cell_y_max, cell_x_max], [cell_y_max, cell_x_min]],
                    # 左边
                    [[cell_y_max, cell_x_min], [cell_y_min, cell_x_min]],
                ]

                # 检查任何多边形边是否与任何单元格边相交
                n_vertices = len(polygon)
                for v in range(n_vertices):
                    v_next = (v + 1) % n_vertices
                    poly_edge = [polygon[v], polygon[v_next]]

                    for cell_edge in cell_edges:
                        if line_segments_intersect(
                            poly_edge[0], poly_edge[1], cell_edge[0], cell_edge[1]
                        ):
                            cell_intersects_polygon = True
                            break

                    if cell_intersects_polygon:
                        break

                # 如果单元格不与多边形相交且所有角点都在外部，
                # 则单元格中的所有点都在外部
                if not cell_intersects_polygon:
                    continue

            # 对于与多边形边界相交的单元格，单独检查每个点
            if len(cell_points) > 100000:
                # 对于非常大的单元格使用Numba
                cell_results = _point_in_polygon_numba(cell_points, polygon)
            else:
                # 对于较小的单元格使用向量化方法
                cell_results = points_in_polygon_vectorized_simple(cell_points, polygon)

            result[cell_mask] = cell_results

    return result


def is_point_in_polygon_precise(point, polygon):
    """
    精确判断点是否在多边形内部

    使用射线投射算法（Ray Casting Algorithm）的优化版本，比简单的外接矩形检查更精确。
    这个函数是points_in_polygon_vectorized的简化版本，专门用于单点检测。

    Args:
        point: 要检查的点的坐标 [y, x]
        polygon: 多边形顶点数组，每行是[y, x]

    Returns:
        bool: 如果点在多边形内部返回True，否则返回False
    """
    # 将点转换为numpy数组
    if not isinstance(point, np.ndarray):
        point = np.array(point)

    # 确保点是二维数组，形状为(1, 2)
    if point.ndim == 1:
        point = point.reshape(1, 2)

    # 使用向量化的点在多边形内检测函数
    return points_in_polygon_vectorized(point, polygon)[0]


def line_segments_intersect(p1, p2, p3, p4):
    """
    检查线段p1-p2是否与线段p3-p4相交

    Args:
        p1, p2: 第一条线段的端点，每个是[y, x]
        p3, p4: 第二条线段的端点，每个是[y, x]

    Returns:
        bool: 如果线段相交则为True
    """
    # 转换为numpy数组以便于计算
    p1 = np.array(p1)
    p2 = np.array(p2)
    p3 = np.array(p3)
    p4 = np.array(p4)

    # 计算方向向量
    d1 = p2 - p1
    d2 = p4 - p3
    d3 = p3 - p1

    # 计算叉积
    cross_d1_d2 = np.cross(d1, d2)

    # 如果线平行，它们不相交（除非共线，我们忽略这种情况）
    if abs(cross_d1_d2) < 1e-10:
        return False

    # 计算交点的参数
    t1 = np.cross(d3, d2) / cross_d1_d2
    t2 = np.cross(d3, d1) / cross_d1_d2

    # 检查交点是否在两条线段内
    return (0 <= t1 <= 1) and (0 <= t2 <= 1)
