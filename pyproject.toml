[build-system]
requires = [
    "setuptools>=45",
    "wheel",
    "pybind11>=2.6.0",
    "pybind11[global]",
]
build-backend = "setuptools.build_meta"

[project]
name = "pathfinding-cpp"
version = "0.1.0"
description = "Multi-Agent Pathfinding with JPS algorithm - C++ Python bindings"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
maintainers = [
    {name = "Your Name", email = "<EMAIL>"},
]
keywords = ["pathfinding", "jps", "jump-point-search", "multi-agent", "cpp", "pybind11"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research", 
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8", 
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: C++",
    "Topic :: Scientific/Engineering",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.7"
dependencies = [
    "pybind11>=2.6.0",
    "numpy>=1.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov",
    "black",
    "flake8",
    "mypy",
    "pre-commit",
]
test = [
    "pytest>=6.0", 
    "pytest-cov",
    "pytest-benchmark",
]
docs = [
    "sphinx",
    "sphinx-rtd-theme",
    "myst-parser",
]

[project.urls]
Homepage = "https://github.com/yourusername/Multi_agent_pathfinding_cpp"
Documentation = "https://github.com/yourusername/Multi_agent_pathfinding_cpp/wiki"
Repository = "https://github.com/yourusername/Multi_agent_pathfinding_cpp"
"Bug Tracker" = "https://github.com/yourusername/Multi_agent_pathfinding_cpp/issues"
Changelog = "https://github.com/yourusername/Multi_agent_pathfinding_cpp/blob/main/CHANGELOG.md"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["pathfinding_cpp"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".tox",
]

[tool.mypy]
python_version = "3.7"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pathfinding_cpp.*",
]
ignore_missing_imports = true
