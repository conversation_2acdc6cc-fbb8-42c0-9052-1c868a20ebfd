// 障碍物类型管理器
#pragma once

#include "common_types.h" // 引入Point3D等基础类型定义
#include <string>         // 用于字符串处理
#include <vector>         // 用于存储数据
#include <map>            // 用于映射关系
#include <set>            // 用于函数参数和返回值
#include <unordered_set>  // 用于内部高效存储
#include <stdexcept>      // 用于异常处理

// 障碍物类型信息结构体
struct ObstacleTypeInfo
{
    std::string description; // 障碍物类型描述
    std::string created_at;  // 创建时间（字符串格式，可升级为std::chrono::time_point）
};

// 障碍物类型管理器类
// 用于管理和维护各种禁飞区的类型信息和空间占用情况
class ObstacleTypeManager
{
public:
    // 障碍物类型管理器构造函数
    // @param height: 地图高度（栅格数）
    // @param width: 地图宽度（栅格数）
    // @param depth: 地图深度（栅格数）
    ObstacleTypeManager(int height, int width, int depth)
        : height_(height), width_(width), depth_(depth), _positions(), _all_non_traversable_nodes() {}

    // 添加新的障碍物类型
    // @param name: 障碍物类型名称
    // @param description: 障碍物类型描述
    // @param created_at_str: 创建时间字符串
    // @throw std::runtime_error 如果类型已存在
    void add_obstacle_type(const std::string &name, const std::string &description, const std::string &created_at_str)
    {
        if (_types.count(name))
        {
            throw std::runtime_error("障碍物类型 '" + name + "' 已存在。");
        }
        _types[name] = {description, created_at_str};
        _positions[name] = {}; // 初始化空坐标集合
    }

    // 移除指定的障碍物类型及其所有坐标
    // @param name: 要移除的障碍物类型名称
    void remove_obstacle_type(const std::string &name)
    {
        if (!_types.count(name))
        {
            return; // 类型不存在时直接返回
        }
        _types.erase(name);
        _positions.erase(name);
        rebuild_all_non_traversable_nodes(); // 更新不可通行节点集合
    }

    // 为指定类型添加障碍物坐标
    // @param type_name: 障碍物类型名称
    // @param positions_to_add: 要添加的坐标集合
    // @throw std::runtime_error 如果类型不存在
    void add_obstacle_positions(const std::string &type_name, const std::set<Point3D> &positions_to_add)
    {
        if (!_types.count(type_name))
        {
            throw std::runtime_error("障碍物类型 '" + type_name + "' 不存在，无法添加坐标。");
        }
        _positions[type_name].insert(positions_to_add.begin(), positions_to_add.end());
        rebuild_all_non_traversable_nodes(); // 更新不可通行节点集合
    }

    // 从指定类型中移除障碍物坐标
    // @param type_name: 障碍物类型名称
    // @param positions_to_remove: 要移除的坐标集合
    void remove_obstacle_positions(const std::string &type_name, const std::set<Point3D> &positions_to_remove)
    {
        if (!_positions.count(type_name))
        {
            return; // 类型不存在时直接返回
        }
        for (const auto &pos : positions_to_remove)
        {
            _positions[type_name].erase(pos);
        }
        rebuild_all_non_traversable_nodes(); // 更新不可通行节点集合
    }

    // 获取所有不可通行节点的集合
    // @return: 返回所有被标记为障碍物的节点集合
    const std::unordered_set<Point3D, Point3DHash> &get_all_non_traversable_nodes() const
    {
        return _all_non_traversable_nodes;
    }

    // 获取指定类型的所有障碍物坐标
    // @param type_name: 障碍物类型名称
    // @return: 返回该类型的所有坐标点集合
    std::unordered_set<Point3D, Point3DHash> get_positions_for_type(const std::string &type_name) const
    {
        if (_positions.count(type_name))
        {
            return _positions.at(type_name);
        }
        return {}; // 类型不存在时返回空集合
    }

    // 获取指定类型的信息
    // @param type_name: 障碍物类型名称
    // @return: 返回障碍物类型信息
    // @throw std::runtime_error 如果类型不存在
    ObstacleTypeInfo get_type_info(const std::string &type_name) const
    {
        if (_types.count(type_name))
        {
            return _types.at(type_name);
        }
        throw std::runtime_error("障碍物类型 '" + type_name + "' 不存在。");
    }

    // 检查指定点是否为障碍物
    // @param pos: 待检查的位置
    // @return: 如果该点是任意类型的障碍物返回true，否则返回false
    bool is_obstacle(const Point3D &pos) const
    {
        return _all_non_traversable_nodes.count(pos);
    }

    // 检查指定点是否为特定类型的障碍物
    // @param type_name: 障碍物类型名称
    // @param pos: 待检查的位置
    // @return: 如果该点是指定类型的障碍物返回true，否则返回false
    bool is_obstacle_of_type(const std::string &type_name, const Point3D &pos) const
    {
        if (_positions.count(type_name))
        {
            return _positions.at(type_name).count(pos);
        }
        return false;
    }

    // 获取所有已定义的障碍物类型名称
    // @return: 返回所有障碍物类型名称的列表
    std::vector<std::string> get_all_type_names() const
    {
        std::vector<std::string> names;
        for (const auto &pair : _types)
        {
            names.push_back(pair.first);
        }
        return names;
    }

private:
    // 地图尺寸
    int height_; // 地图高度（栅格数）
    int width_;  // 地图宽度（栅格数）
    int depth_;  // 地图深度（栅格数）

    // 核心数据结构
    std::map<std::string, ObstacleTypeInfo> _types;                             // 存储障碍物类型信息
    std::map<std::string, std::unordered_set<Point3D, Point3DHash>> _positions; // 存储各类型障碍物的坐标集合
    std::unordered_set<Point3D, Point3DHash> _all_non_traversable_nodes;        // 存储所有不可通行节点的集合

    // 重建不可通行节点集合
    // 当任何类型的障碍物坐标发生变化时，需要调用此方法更新全局的不可通行节点集合
    void rebuild_all_non_traversable_nodes()
    {
        _all_non_traversable_nodes.clear(); // 清空当前集合
        for (const auto &pair : _positions)
        {
            // 将所有类型的障碍物坐标添加到不可通行节点集合中
            _all_non_traversable_nodes.insert(pair.second.begin(), pair.second.end());
        }
    }
};
