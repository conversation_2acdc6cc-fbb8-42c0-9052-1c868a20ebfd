"""
统一定时器管理器模块
使用单个线程顺序处理所有定时任务，降低内存使用
"""

from typing import Dict, List, Callable, Any, Optional
import threading
import time
import logging
from ..utils.logging import get_logger

logger = get_logger(__name__)


class UnifiedTimerManager:
    """统一定时器管理器，使用单个线程顺序处理所有定时任务"""

    # 单例模式
    _instance = None
    _lock = threading.Lock()

    # 定时任务列表
    _tasks: Dict[str, Dict[str, Any]] = {}

    # 定时器线程
    _timer_thread = None

    # 运行标志
    _running = False

    # 关闭标志
    is_shutting_down = False

    # 统一的定时器间隔（秒）
    timer_interval = 60  # 默认为60秒

    def __new__(cls, timer_interval: int = 60):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(UnifiedTimerManager, cls).__new__(cls)
                cls._instance._tasks = {}
                cls._instance._running = False
                cls._instance.is_shutting_down = False
                cls._instance.timer_interval = timer_interval
            return cls._instance

    def register_task(
        self, task_name: str, callback: Callable, *args, **kwargs
    ) -> bool:
        """
        注册定时任务

        Args:
            task_name: 任务名称
            callback: 回调函数
            *args: 回调函数的位置参数
            **kwargs: 回调函数的关键字参数

        Returns:
            bool: 是否成功注册任务
        """
        with self._lock:
            # 如果任务已存在，先取消
            if task_name in self._tasks:
                logger.debug(f"任务 {task_name} 已存在，将被覆盖")

            # 注册任务
            self._tasks[task_name] = {
                "callback": callback,
                "args": args,
                "kwargs": kwargs,
                "last_run": None,
                "next_run": time.time() + self.timer_interval,
                "enabled": True,  # 任务是否启用
                "error_count": 0,  # 错误计数
            }

            logger.info(f"任务 {task_name} 已注册")

            # 如果定时器线程未运行，启动它
            if not self._running:
                self._start_timer_thread()

            return True

    def unregister_task(self, task_name: str) -> bool:
        """
        取消注册定时任务

        Args:
            task_name: 任务名称

        Returns:
            bool: 是否成功取消注册任务
        """
        with self._lock:
            if task_name in self._tasks:
                del self._tasks[task_name]
                logger.info(f"任务 {task_name} 已取消注册")
                return True
            return False

    def enable_task(self, task_name: str) -> bool:
        """启用任务"""
        with self._lock:
            if task_name in self._tasks:
                self._tasks[task_name]["enabled"] = True
                logger.info(f"任务 {task_name} 已启用")
                return True
            return False

    def disable_task(self, task_name: str) -> bool:
        """禁用任务"""
        with self._lock:
            if task_name in self._tasks:
                self._tasks[task_name]["enabled"] = False
                logger.info(f"任务 {task_name} 已禁用")
                return True
            return False

    def _start_timer_thread(self) -> None:
        """启动定时器线程"""
        if self._running:
            logger.debug("定时器线程已经在运行")
            return

        self._running = True
        self._timer_thread = threading.Thread(
            target=self._timer_loop, name="UnifiedTimerThread", daemon=True
        )
        self._timer_thread.start()
        logger.info(f"统一定时器线程已启动，间隔: {self.timer_interval}秒")

    def _timer_loop(self) -> None:
        """定时器线程主循环"""
        logger.info("统一定时器循环开始运行")

        # 确保线程ID已设置
        thread_id = threading.get_ident()
        if thread_id is None:
            logger.error("定时器线程ID为None，无法正常运行")
            return

        while self._running and not self.is_shutting_down:
            try:
                # 记录循环开始时间
                loop_start_time = time.time()

                # 获取任务列表的快照
                with self._lock:
                    tasks_snapshot = self._tasks.copy()

                # 顺序执行所有启用的任务
                for task_name, task_info in tasks_snapshot.items():
                    # 再次检查运行状态，以便快速响应关闭请求
                    if not self._running or self.is_shutting_down:
                        break

                    if not task_info["enabled"]:
                        continue

                    try:
                        # 执行任务
                        task_start_time = time.time()
                        logger.debug(f"开始执行任务: {task_name}")

                        # 调用回调函数，捕获所有可能的异常
                        try:
                            task_info["callback"](
                                *task_info["args"], **task_info["kwargs"]
                            )
                        except Exception as task_error:
                            logger.error(
                                f"任务 {task_name} 执行失败: {str(task_error)}"
                            )
                            raise  # 重新抛出异常，让外层异常处理逻辑处理

                        # 更新任务信息
                        with self._lock:
                            if task_name in self._tasks:  # 确保任务仍然存在
                                self._tasks[task_name]["last_run"] = task_start_time
                                self._tasks[task_name]["next_run"] = (
                                    task_start_time + self.timer_interval
                                )
                                self._tasks[task_name][
                                    "error_count"
                                ] = 0  # 重置错误计数

                        # 只记录耗时较长的任务执行时间，减少日志量
                        task_time = time.time() - task_start_time
                        if task_time > 0.5:  # 只记录耗时超过0.5秒的任务
                            logger.debug(
                                f"任务 {task_name} 执行完成，耗时: {task_time:.3f}秒"
                            )

                    except Exception as e:
                        # 记录错误并继续执行下一个任务
                        logger.error(f"执行任务 {task_name} 时出错: {str(e)}")

                        # 更新错误计数
                        with self._lock:
                            if task_name in self._tasks:  # 确保任务仍然存在
                                self._tasks[task_name]["error_count"] += 1
                                # 如果连续错误次数过多，禁用任务
                                if self._tasks[task_name]["error_count"] >= 5:
                                    logger.warning(
                                        f"任务 {task_name} 连续失败5次，已禁用"
                                    )
                                    self._tasks[task_name]["enabled"] = False

                # 计算循环耗时
                loop_time = time.time() - loop_start_time
                # 只记录耗时较长的循环，减少日志量
                if loop_time > 1.0:  # 只记录耗时超过1秒的循环
                    logger.debug(
                        f"定时器循环完成一轮任务执行，总耗时: {loop_time:.3f}秒"
                    )

                # 计算需要等待的时间
                wait_time = max(0.1, self.timer_interval - loop_time)

                # 使用单次等待，减少CPU唤醒次数
                # 设置较长的等待时间，但每隔一段时间检查一次关闭标志
                wait_end_time = time.time() + wait_time

                # 每次检查间隔（秒）
                check_interval = 1.0

                while time.time() < wait_end_time:
                    # 检查关闭标志
                    if not self._running or self.is_shutting_down:
                        break

                    # 计算剩余等待时间
                    remaining_time = wait_end_time - time.time()
                    if remaining_time <= 0:
                        break

                    # 等待较短的时间（检查间隔或剩余时间）
                    sleep_time = min(check_interval, remaining_time)
                    time.sleep(sleep_time)

            except Exception as e:
                logger.error(f"定时器循环出错: {str(e)}")
                # 出错后等待一段时间再继续
                time.sleep(5)

                # 检查线程是否应该继续运行
                if not self._running or self.is_shutting_down:
                    break

        logger.info("统一定时器循环已退出")

    def shutdown(self) -> None:
        """关闭定时器管理器"""
        logger.info("正在关闭统一定时器管理器...")

        # 设置关闭标志
        self.is_shutting_down = True
        self._running = False

        # 清空任务列表（先清空任务，避免线程继续执行任务）
        with self._lock:
            self._tasks.clear()

        # 等待定时器线程结束
        if self._timer_thread and self._timer_thread.is_alive():
            try:
                # 减少等待时间，加快关闭速度
                self._timer_thread.join(timeout=1.0)  # 最多等待1秒

                if self._timer_thread.is_alive():
                    logger.warning("定时器线程未能在1秒内结束，将强制设置为守护线程")
                    # 将线程设置为守护线程，这样主程序退出时它会自动终止
                    try:
                        self._timer_thread.daemon = True
                    except Exception as daemon_error:
                        logger.error(
                            f"设置定时器线程为守护线程失败: {str(daemon_error)}"
                        )
                else:
                    logger.info("定时器线程已正常结束")
            except Exception as e:
                logger.error(f"等待定时器线程结束时出错: {str(e)}")
                # 确保线程被标记为守护线程
                try:
                    self._timer_thread.daemon = True
                except:
                    pass

        # 确保线程引用被清除
        self._timer_thread = None

        logger.info("统一定时器管理器已关闭")
