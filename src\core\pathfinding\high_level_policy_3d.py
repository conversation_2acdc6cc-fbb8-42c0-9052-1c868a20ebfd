from heapq import heappush, heappop
import math
from ..node_3d import CTNode3D, GridNode3D
from ...utils.logging import get_logger

logger = get_logger(__name__)
# from .low_level_policy_3d import AStar3D

# from .astar_v2 import OptimizedPathFinder

# from .jps import OptimizedPathFinder

# from .jps_v2 import OptimizedPathFinder

# from .jps_v3 import OptimizedPathFinder

from .jps_v4 import OptimizedPathFinder


# from optimized_path_finder_jps_theta import OptimizedPathFinderJPSTheta
# from optimized_path_finder_fan_shape import OptimizedPathFinderFanShape
# from optimized_path_finder_fan_shape_continuous import OptimizedPathFinderFanShapeContinuous
from ..map.occupancy_map import OccupancyMap
from typing import List, Tuple, Dict, Optional


class CBS3D:
    def __init__(self, map3d: object, occupancy_map=None) -> None:
        """初始化 CBS 规划器

        Args:
            map3d: 三维地图对象
            occupancy_map: 可选，外部传入的占用图实例
        """
        self.map = map3d  # 真实三维空间地图，包括固定障碍网格
        # self.lowlevel = AStar3D(map3d)
        self.lowlevel = OptimizedPathFinder(map3d)
        # self.lowlevel = OptimizedPathFinderFanShape(map3d)
        # self.lowlevel = OptimizedPathFinderFanShapeContinuous(map3d)
        self.constraints = []  # CBS约束列表
        self.entry_count = 0  # 记录入队次数， 作为加入open时的优先级
        self.open = []  # 初始化优先队列
        self.occupancy_map = (
            occupancy_map
            if occupancy_map
            else OccupancyMap(
                (self.map.height, self.map.width, self.map.depth), time_buffer=1
            )
        )

    def get_first_conflict(
        self, node: CTNode3D
    ) -> Optional[Tuple[str, Tuple[str, str], Tuple[int, int, int, int]]]:
        """检测路径冲突，包括与已审批飞行计划的冲突

        Args:
            node: CT节点，包含当前所有智能体的路径

        Returns:
            冲突信息: (冲突类型, (agent1_id, agent2_id), (y, x, z, t))
            如果没有冲突返回None
        """
        paths = node.solution
        conflicts = []

        # 使用当前占用图的副本进行碰撞检测
        temp_map = self.occupancy_map.copy()

        # 检查每个智能体的路径
        for agent_id, path in paths.items():
            for node in path:
                has_collision, colliding_agent = temp_map.check_collision(
                    (node.y, node.x, node.z), node.t
                )
                if has_collision and colliding_agent != agent_id:
                    # 如果与已审批的路径冲突，直接返回
                    if colliding_agent not in paths:
                        return (
                            "v",
                            (agent_id, colliding_agent),
                            (node.y, node.x, node.z, node.t),
                        )
                    # 如果与其他新规划的路径冲突，只处理一次（避免重复）
                    elif (
                        colliding_agent < agent_id
                    ):  # 使用ID字符串比较确保冲突只被处理一次
                        conflicts.append(
                            (
                                "v",
                                (agent_id, colliding_agent),
                                (node.y, node.x, node.z, node.t),
                            )
                        )

            # 将当前路径添加到临时占用图中，用于检测后续路径
            temp_map.add_path(path, agent_id)

        # 如果有新规划路径间的冲突，返回第一个
        return conflicts[0] if conflicts else None

    def _bypass_conflict(
        self,
        conflict: Tuple[str, Tuple[str, str], Tuple[int, int, int, int]],
        node: CTNode3D,
    ) -> List[GridNode3D]:
        """Try to bypass the conflict by waiting"""
        agent_id = conflict[1][0]
        path = node.solution[agent_id][0]
        t = conflict[2][-1]

        # Try to insert wait action
        new_path = path[:t]
        new_path.append(path[t - 1])  # Wait at previous position
        new_path.extend(path[t:])

        return new_path

    # TODO:获取审评端信息，将审评通过计划路径添加到已有智能体的路径缓冲区
    # def add_path_to_buffer(self, path: List[GridNode3D], agent_id: str) -> None:
    #     """将路径添加到已有智能体的路径缓冲区

    #     Args:
    #         path: 智能体的路径
    #         agent_id: 智能体ID
    #     """
    #     # self.existing_paths[agent_id] = (path, len(path))

    def solve(
        self,
        starts: List[Tuple[int, int, int]],
        goals: List[Tuple[int, int, int]],
        min_height: int,
        agent_ids: List[str] = None,
        start_times: Optional[List[int]] = None,
        time_limit: int = math.inf,
    ) -> Tuple[Optional[Dict[str, List[GridNode3D]]], Optional[Dict[str, str]]]:
        """使用CBS算法解决多智能体路径规划问题

        Args:
            starts: 起点列表，每个元素为 (y, x, z)
            goals: 终点列表，每个元素为 (y, x, z)
            min_height: 最小巡航高度
            agent_ids: 智能体ID列表，后续应修改为飞行计划id
            start_times: 每个智能体的起飞时间列表，与agent_ids一一对应
            time_limit: 最大允许搜索节点数

        Returns:
            Tuple[Optional[Dict[str, List[GridNode3D]]], Optional[Dict[str, str]]]:
            第一个元素是每个智能体ID对应其路径的字典，第二个元素是每个智能体的失败原因字典
        """
        # 初始化根节点的解决方案
        solution = {}
        cost = 0
        failure_reasons = {}

        if agent_ids is None:
            agent_ids = [str(i) for i in range(len(starts))]

        if start_times is None:
            start_times = [0] * len(agent_ids)

        # 为每个智能体规划初始路径
        for i, (start, goal, agent_id) in enumerate(zip(starts, goals, agent_ids)):
            path, error = self.lowlevel.find_path(
                start=start,
                goal=goal,
                min_height=min_height,
                agent_id=agent_id,
                start_time=start_times[i],  # 使用列表索引获取起飞时间
                occupancy_map=self.occupancy_map,
            )
            # print(f"agent_id: {agent_id}, start: {start}, goal: {goal}, path: {path}")
            if path is None:
                failure_reasons[agent_id] = error
                return None, failure_reasons
            solution[agent_id] = path
            cost += len(path)
            return solution, None

        ##--------------------后面代码暂时不需要---------------------------------#######

        # 创建根节点，初始没有约束
        root = CTNode3D(
            constraints=set(),
            solution=solution,
            cost=cost,
            map_size=(self.map.height, self.map.width, self.map.depth),
        )
        heappush(self.open, (root.cost, self.entry_count, root))
        self.entry_count += 1

        # 开始搜索
        while self.open and len(self.open) < time_limit:
            _, _, curr = heappop(self.open)

            # 检查是否有冲突
            conflict = self.get_first_conflict(curr)
            if not conflict:
                # 将所有路径添加到occupancy_map
                solution = {agent_id: curr.solution[agent_id] for agent_id in agent_ids}
                for agent_id, path in solution.items():
                    self.occupancy_map.add_path(path, agent_id)
                return solution, None, all_nodes

            # 处理冲突
            c_type, (agent1, agent2), (y, x, z, t) = conflict

            # 为两个智能体创建新的约束
            for agent_id in [agent1, agent2]:
                # 创建新的约束集合
                new_constraints = curr.constraints.copy()
                # 添加新的约束：('v', (agent_id, y, x, z, t))
                new_constraints.add((c_type, (agent_id, y, x, z, t)))

                # 使用新约束重新规划路径
                path, error, all_nodes = self.lowlevel.find_path(
                    start=starts[agent_ids.index(agent_id)],
                    goal=goals[agent_ids.index(agent_id)],
                    min_height=min_height,
                    agent_id=agent_id,
                    constraints=list(new_constraints),
                    occupancy_map=self.occupancy_map,
                    start_time=start_times[agent_ids.index(agent_id)],
                )

                # 先不考虑悬停
                # if path is None:
                #     # Try to bypass conflict by waiting
                #     new_path = self._bypass_conflict(conflict, current)
                #     if new_path:
                #         path = new_path
                # length = len(new_path)

                if path:  # 如果找到了新路径
                    # 创建新节点
                    new_solution = curr.solution.copy()
                    new_solution[agent_id] = path
                    new_cost = sum(len(path) for path in new_solution.values())

                    new_node = CTNode3D(
                        constraints=new_constraints,
                        solution=new_solution,
                        cost=new_cost,
                        parent=curr,
                        entry=self.entry_count,
                        map_size=(self.map.height, self.map.width, self.map.depth),
                    )
                    heappush(self.open, (new_node.cost, self.entry_count, new_node))
                    self.entry_count += 1
                else:
                    failure_reasons[agent_id] = error

        # 如果超时或无解，返回None和失败原因
        if not failure_reasons:
            failure_reasons[agent_ids[0]] = "无法找到满足所有约束的解决方案"
        return None, failure_reasons, None
