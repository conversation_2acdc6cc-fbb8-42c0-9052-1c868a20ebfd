{"tasks": [{"type": "cppbuild", "label": "C/C++: g++.exe build project with Clipper2", "command": "D:\\Program Files\\mingw64\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "-std=c++17", "-I${workspaceFolder}/cpp_implementation/libs/clipper2/include", "-I${workspaceFolder}/cpp_implementation/include", "${workspaceFolder}/cpp_implementation/test/main.cpp", "${workspaceFolder}/cpp_implementation/src/Map3D.cpp", "${workspaceFolder}/cpp_implementation/src/OccupancyMap.cpp", "${workspaceFolder}/cpp_implementation/src/JPS.cpp", "${workspaceFolder}/cpp_implementation/libs/clipper2/src/clipper.engine.cpp", "${workspaceFolder}/cpp_implementation/libs/clipper2/src/clipper.offset.cpp", "${workspaceFolder}/cpp_implementation/libs/clipper2/src/clipper.rectclip.cpp", "-o", "${workspaceFolder}/cpp_implementation/test/Output.exe"], "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Builds the project including Clipper2 library."}, {"type": "cppbuild", "label": "C/C++: g++.exe 生成活动文件", "command": "D:\\Program Files\\mingw64\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": false}, "detail": "调试器生成的任务。"}], "version": "2.0.0"}