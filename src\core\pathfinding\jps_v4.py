# -*- coding: utf-8 -*-
import numpy as np
import heapq
from multiprocessing import heap
from os import path
import math
from typing import List, Set, Dict, Tuple, Optional

# from networkx import immediate_dominators

# from networkx import neighbors
# import test
from ..node_3d import GridNode3D
from collections import OrderedDict
from ...config import settings  # Corrected import


class LRUCache:
    """具有大小限制的 LRU (Least Recently Used) 缓存实现
    用于存储经常访问的数据，当缓存达到容量上限时，自动删除最久未使用的项
    """

    def __init__(self, capacity: int):
        """初始化LRU缓存

        Args:
            capacity: 缓存的最大容量
        """
        self.cache = OrderedDict()  # 使用OrderedDict来维护项目的访问顺序
        self.capacity = capacity

    def get(self, key):
        """获取缓存中的值，如果存在则将其移到最近使用的位置

        Args:
            key: 要获取的键

        Returns:
            如果键存在则返回对应的值，否则返回None
        """
        if key not in self.cache:
            return None
        # 移动到最近使用的位置
        self.cache.move_to_end(key)
        return self.cache[key]

    def put(self, key, value):
        """将键值对添加到缓存中

        Args:
            key: 要添加的键
            value: 要添加的值
        """
        if key in self.cache:
            self.cache.move_to_end(key)
        self.cache[key] = value
        if len(self.cache) > self.capacity:
            self.cache.popitem(last=False)  # 删除最久未使用的项


class OptimizedPathFinder:
    """优化的3D路径规划器，使用跳点搜索（JPS）算法
    实现了垂直起飞、巡航和降落三个阶段的路径规划
    """

    def __init__(self, map3d):
        """初始化路径规划器

        Args:
            map3d: 3D地图对象，包含环境信息
        """
        self.map = map3d  # 3D地图实例
        self.height = map3d.height  # 地图高度
        self.width = map3d.width  # 地图宽度
        self.depth = map3d.depth  # 地图深度

        self.takeoff_speed_t = (
            1 / settings.settings.pathplanning.takeoff_speed
        )  # Corrected access
        self.cruise_speed_t = (
            1 / settings.settings.pathplanning.cruise_speed
        )  # Corrected access
        self.landing_speed_t = (
            1 / settings.settings.pathplanning.landing_speed
        )  # Corrected access

        self.only_turning_points = (
            settings.settings.pathplanning.only_turning_points
        )  # Corrected access

        # 定义移动方向优先级（按重要性排序）
        self.DIRECTION_PRIORITIES = [
            # 第一优先级：XY平面基本方向
            (0, 1, 0),  # 右
            (0, -1, 0),  # 左
            (1, 0, 0),  # 前
            (-1, 0, 0),  # 后
            # 第二优先级：XY平面对角线（斜向移动）
            (1, 1, 0),  # 右前
            (1, -1, 0),  # 左前
            (-1, 1, 0),  # 右后
            (-1, -1, 0),  # 左后
            # 第三优先级：Z轴移动（高度变化，能耗较大）
            (0, 0, 1),  # 上升
            (0, 0, -1),  # 下降
        ]

        # 预计算强制邻居的检查方向
        # 强制邻居是指：在某个方向移动被阻挡时，需要检查的替代路径点
        self.FORCED_NEIGHBOR_DIRS = {
            # 基本方向移动时的检查点
            # 当向后移动(-1,0,0)时需要检查的点
            (-1, 0, 0): [
                ((0, 1, 0)),  # 检查右侧和右后
                ((0, -1, 0)),  # 检查左侧和左后
                ((0, 0, 1)),  # 检查上方和后上
                ((0, 0, -1)),  # 检查下方和后下
            ],
            # 当向前移动(1,0,0)时需要检查的点
            (1, 0, 0): [
                ((0, 1, 0)),  # 检查右侧和右前
                ((0, -1, 0)),  # 检查左侧和左前
                ((0, 0, 1)),  # 检查上方和前上
                ((0, 0, -1)),  # 检查下方和前下
            ],
            # 当向左移动(0,-1,0)时需要检查的点
            (0, -1, 0): [
                ((1, 0, 0)),  # 检查前方和左前
                ((-1, 0, 0)),  # 检查后方和左后
                ((0, 0, 1)),  # 检查上方和左上
                ((0, 0, -1)),  # 检查下方和左下
            ],
            # 当向右移动(0,1,0)时需要检查的点
            (0, 1, 0): [
                ((1, 0, 0)),  # 检查前方和右前
                ((-1, 0, 0)),  # 检查后方和右后
                ((0, 0, 1)),  # 检查上方和右上
                ((0, 0, -1)),  # 检查下方和右下
            ],
            # 当向下移动(0,0,-1)时需要检查的点
            (0, 0, -1): [
                ((1, 0, 0)),  # 检查前方和前下
                ((-1, 0, 0)),  # 检查后方和后下
                ((0, 1, 0)),  # 检查右侧和右下
                ((0, -1, 0)),  # 检查左侧和左下
            ],
            # 当向上移动(0,0,1)时需要检查的点
            (0, 0, 1): [
                ((1, 0, 0)),  # 检查前方和前上
                ((-1, 0, 0)),  # 检查后方和后上
                ((0, 1, 0)),  # 检查右侧和右上
                ((0, -1, 0)),  # 检查左侧和左上
            ],
            # 对角线移动时的检查点（包含三步检查路径）
            # 当向右前方移动(1,1,0)时需要检查的点
            (1, 1, 0): [
                # 垂直方向的检查
                ((0, 0, 1)),  # 检查上方和右前上
                ((0, 0, -1)),  # 检查下方和右前下
                # 如果前方(1,0,0)可走，检查经过(2,0,0)到达(2,1,0)的路径
                ((1, 0, 0)),
                # 如果右侧(0,1,0)可走，检查经过(0,2,0)到达(1,2,0)的路径
                ((0, 1, 0)),
            ],
            # 其他对角线移动方向的检查点类似，遵循相同的模式
            (1, -1, 0): [
                # 垂直方向的检查
                ((0, 0, 1)),
                ((0, 0, -1)),
                # 如果前方(1,0,0)可走，先到(2,0,0)再到(2,-1,0)
                ((1, 0, 0)),
                # 如果左侧(0,-1,0)可走，先到(0,-2,0)再到(1,-2,0)
                ((0, -1, 0)),
            ],
            # 右后对角线移动(-1,1,0)被阻塞时
            (-1, 1, 0): [
                # 垂直方向的检查
                ((0, 0, 1)),
                ((0, 0, -1)),
                # 如果后方(-1,0,0)可走，先到(-2,0,0)再到(-2,1,0)
                ((-1, 0, 0)),
                # 如果右侧(0,1,0)可走，先到(0,2,0)再到(-1,2,0)
                ((0, 1, 0)),
            ],
            # 左后对角线移动(-1,-1,0)被阻塞时
            (-1, -1, 0): [
                # 垂直方向的检查
                ((0, 0, 1)),
                ((0, 0, -1)),
                # 如果后方(-1,0,0)可走，先到(-2,0,0)再到(-2,-1,0)
                ((-1, 0, 0)),
                # 如果左侧(0,-1,0)可走，先到(0,-2,0)再到(-1,-2,0)
                ((0, -1, 0)),
            ],
        }

        # 初始化各种缓存，用于提高性能
        CACHE_SIZE = 10000  # 设置合理的缓存大小
        # self.neighbor_cache = LRUCache(CACHE_SIZE)  # 缓存邻居节点
        self.static_neighbor_cache = LRUCache(50000)  # 静态邻居缓存
        self.valid_position_cache = LRUCache(CACHE_SIZE)  # 缓存有效位置
        self.constraint_cache = LRUCache(CACHE_SIZE)  # 缓存约束检查结果
        self.distance_cache = LRUCache(CACHE_SIZE)  # 缓存距离计算结果

    def _check_constraints_and_collisions(
        self, pos, t, agent_id, constraints, occupancy_map
    ):
        """检查位置和时间是否满足约束条件和碰撞检测

        Args:
            pos: 位置坐标 (y, x, z)
            t: 时间戳，表示当前时刻
            agent_id: 智能体ID，用于区分不同无人机
            constraints: 约束列表，包含时空约束条件
            occupancy_map: 占用图对象，记录其他无人机的位置

        Returns:
            Tuple[bool, Optional[str]]: (是否可行, 错误原因)，如果可行返回(True, None)
        """
        # 首先检查地图中的静态障碍物类型
        if not self.map.traversable(pos[0], pos[1], pos[2]):
            obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
            return False, f"位置 {pos} 与障碍物类型 {obstacle_types} 发生碰撞"

        # 检查与其他无人机的碰撞
        if occupancy_map:
            has_collision, colliding_agent = occupancy_map.check_collision(pos, t)
            if has_collision and colliding_agent != agent_id:
                return False, f"与无人机 {colliding_agent} 在时刻 {t} 发生碰撞"

        # 检查时空约束条件
        if constraints:
            for constraint in constraints:
                c_type, c_data = constraint
                if c_type == "v":  # 顶点约束（vertex constraint）
                    c_agent, c_y, c_x, c_z, c_t = c_data
                    if (
                        agent_id == c_agent
                        and pos[0] == c_y
                        and pos[1] == c_x
                        and pos[2] == c_z
                        and t == c_t
                    ):
                        return False, f"违反约束 {constraint}"

        return True, None

    def _is_valid_position(
        self,
        pos: Tuple[int, int, int],
        t: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
        ignore_min_height: bool = False,
    ) -> Tuple[bool, Optional[str]]:
        """使用缓存优化的位置有效性检查

        Args:
            pos: 位置坐标 (y, x, z)
            t: 时间戳
            min_height: 最小飞行高度限制
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象
            ignore_min_height: 是否忽略最小高度限制，起飞阶段为True

        Returns:
            Tuple[bool, Optional[str]]: (位置是否有效, 错误原因)
        """
        if (
            pos[0] >= self.map.height
            or pos[1] >= self.map.width
            or pos[2] >= self.map.depth
            or pos[0] < 0
            or pos[1] < 0
            or pos[2] < 0
        ):
            real_pos = self.map.grid_converter.relative_to_geo(pos[0], pos[1], pos[2])
            return False, f"位置 {real_pos} 超出地图范围"

        # 快速路径：首先检查位置是否可通行
        if not self.map.traversable(pos[0], pos[1], pos[2]):
            obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
            real_pos = self.map.grid_converter.relative_to_geo(pos[0], pos[1], pos[2])
            return False, f"位置 {real_pos} 与障碍物类型 {obstacle_types} 发生碰撞"

        # 检查最小高度约束（除非明确忽略）
        if not ignore_min_height and pos[2] < min_height:
            real_alt = self.map.grid_converter.relative_to_geo(0, 0, pos[2])["alt"]
            real_min_alt = self.map.grid_converter.relative_to_geo(0, 0, min_height)[
                "alt"
            ]
            return False, f"飞行高度 {real_alt} 低于最小巡航高度 {real_min_alt}"

        # 检查约束和碰撞（使用缓存优化）
        if constraints or occupancy_map:
            # 使用位置和时间的哈希作为缓存键
            cache_key = hash((pos, t))
            cached_result = self.constraint_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 缓存未命中，执行实际检查
            result, error = self._check_constraints_and_collisions(
                pos, t, agent_id, constraints, occupancy_map
            )
            # 保存结果到缓存
            self.constraint_cache.put(cache_key, (result, error))
            return result, error

        return True, None

    def _get_neighbors(
        self,
        node: GridNode3D,
        min_height: int,
        agent_id: str = None,
        constraints: Optional[List] = None,
        occupancy_map=None,
        vertical_look_steps: int = 5,  # 向上查看的步数
    ) -> List[Tuple[int, int, int]]:
        """获取节点的邻居位置，使用优先级顺序和缓存优化
        当遇到障碍物时，会尝试向上查看几步，寻找可能的路径

        Args:
            node: 当前节点
            min_height: 最小飞行高度
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象
            vertical_look_steps: 向上查看的最大步数

        Returns:
            List[Tuple[int, int, int]]: 可行邻居位置列表
        """
        # 静态邻居计算（固定障碍物）
        static_key = hash((node.y, node.x, node.z))
        static_neighbors = self.static_neighbor_cache.get(static_key)

        if static_neighbors is None:
            # 计算基础静态邻居（仅考虑固定障碍物）
            static_neighbors = []
            blocked_directions = []  # 记录被阻挡的方向

            # 首先检查水平方向
            for dy, dx, dz in self.DIRECTION_PRIORITIES:
                # 只处理水平方向的移动
                if dz == 0:
                    ny = node.y + dy * node.jump_step
                    nx = node.x + dx * node.jump_step
                    nz = node.z
                    if (
                        ny >= self.map.height
                        or nx >= self.map.width
                        or nz >= self.map.depth
                        or ny < 0
                        or nx < 0
                        or nz < 0
                    ):
                        continue

                    if node.parent is not None and (ny, nx, nz) == (
                        node.parent.y,
                        node.parent.x,
                        node.parent.z,
                    ):
                        continue

                    if self.map.traversable(ny, nx, nz):
                        static_neighbors.append([(ny, nx, nz)])
                    else:
                        obstacle_types = self.map.obstacle_manager.get_type_at_position(
                            (ny, nx, nz)
                        )
                        if obstacle_types:  # 如果不是无人机路径障碍物就跳过
                            continue

                        # 如果水平方向被阻挡，记录下来，稍后尝试向上
                        blocked_directions.append((dy, dx))

            immediate_neighbors = []
            # 对于被阻挡的方向，尝试向上查看几步
            for dy, dx in blocked_directions:
                for step in range(1, vertical_look_steps + 1, 2):
                    ny = node.y + dy * node.jump_step
                    nx = node.x + dx * node.jump_step
                    nz = node.z + step  # 向上查看

                    # 检查是否超出地图范围
                    if (
                        ny >= self.map.height
                        or nx >= self.map.width
                        or nz >= self.map.depth
                        or ny < 0
                        or nx < 0
                        or nz < 0
                    ):
                        break

                    immediate_neighbors.append((node.y, node.x, nz))

                    # 检查上方位置是否可通行
                    if self.map.traversable(ny, nx, nz):
                        # static_neighbors.append((ny, nx, nz))
                        immediate_neighbors.append((ny, nx, nz))
                        static_neighbors.append(immediate_neighbors)
                        # 找到一个可行的高度就停止向上查找
                        break

            # 添加垂直方向的移动
            # for dz in [1, -1]:  # 上升和下降
            #     nz = node.z + dz
            #     if 0 <= nz < self.map.depth:
            #         if self.map.traversable(node.y, node.x, nz):
            #             static_neighbors.append((node.y, node.x, nz))

            self.static_neighbor_cache.put(static_key, static_neighbors)

        # 无动态因素时直接返回
        if not (constraints or occupancy_map):
            return static_neighbors

        valid_neighbors = []
        for pos_list in static_neighbors:
            # 执行实际动态检查
            all_valid = True
            for pos in pos_list:
                is_valid, _ = self._is_valid_position(
                    pos, node.t + 1, min_height, agent_id, constraints, occupancy_map
                )
                if not is_valid:
                    all_valid = False
                    break
            if all_valid:
                valid_neighbors.append(pos_list)

        return valid_neighbors

    def _manhattan_distance(
        self, pos1: Tuple[int, int, int], pos2: Tuple[int, int, int]
    ) -> int:
        """计算曼哈顿距离，用作启发式估计值

        Args:
            pos1, pos2: 两个位置坐标 (y, x, z)

        Returns:
            int: 两点间的曼哈顿距离
        """
        return abs(pos2[0] - pos1[0]) + abs(pos2[1] - pos1[1]) + abs(pos2[2] - pos1[2])

    def _octile_distance(
        self, pos1: Tuple[int, int, int], pos2: Tuple[int, int, int]
    ) -> float:
        """计算八方向距离，考虑对角移动和直线移动的成本差异
        对角移动成本为直线移动的1.414倍

        Args:
            pos1, pos2: 两个位置坐标 (y, x, z)

        Returns:
            float: 两点间的八方向距离
        """
        # 计算各个轴上的差距
        dy = abs(pos2[0] - pos1[0])
        dx = abs(pos2[1] - pos1[1])
        dz = abs(pos2[2] - pos1[2])

        # 计算xy平面上的对角移动和直线移动
        diag_xy = min(dx, dy)  # 对角移动次数
        straight_xy = max(dx, dy) - diag_xy  # 直线移动次数

        # 对角移动成本为1.414，直线移动成本为1
        # 加上z轴的移动成本
        return diag_xy * 1.414 + straight_xy + dz

    def _euclidean_distance(
        self, pos1: Tuple[int, int, int], pos2: Tuple[int, int, int]
    ) -> float:
        """计算欧几里得距离，用作启发式估计值"""
        return math.sqrt(
            (pos2[0] - pos1[0]) ** 2
            + (pos2[1] - pos1[1]) ** 2
            + (pos2[2] - pos1[2]) ** 2
        )

    def _has_forced_neighbor(
        self,
        current_node,
        direction,
        min_height,
        agent_id=None,
        constraints=None,
        occupancy_map=None,
    ):
        """检查在给定方向上是否存在强制邻居

        强制邻居是指：当某个移动方向被阻挡时，为保证路径完整性必须检查的替代路径点。
        这些点在跳点搜索中起着关键作用，可能会引导搜索到更优的路径。

        Args:
            current_node: 当前节点
            direction: 移动方向
            min_height: 最小飞行高度
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象

        Returns:
            bool: 是否存在强制邻居
        """
        current_pos = (current_node.y, current_node.x, current_node.z)

        # 如果当前方向没有预定义的强制邻居检查模式，直接返回False
        if direction not in self.FORCED_NEIGHBOR_DIRS:
            return False

        # 检查每个可能的强制邻居路径
        for point in self.FORCED_NEIGHBOR_DIRS[direction]:
            check_pos = (
                current_pos[0] + point[0],
                current_pos[1] + point[1],
                current_pos[2] + point[2],
            )

            is_valid, _ = self._is_valid_position(
                check_pos,
                current_node.t + self.cruise_speed_t,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )
            # 如果找到一条完全可行的强制邻居路径，返回True
            if is_valid:
                return True

        return False

    def _jump(
        self,
        current_node: GridNode3D,
        parent_node: Optional[GridNode3D],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
        jump_step,
        vertical_look_steps: int = 5,  # 向上查看的步数
    ) -> Optional[GridNode3D]:
        """跳点搜索的核心函数，沿着一个方向不断前进直到遇到跳点
        当遇到障碍物时，会尝试向上查看几步，寻找可能的路径

        跳点的条件：
        1. 到达目标点
        2. 遇到障碍物
        3. 发现强制邻居
        4. 到达地图边界

        Args:
            current: 当前节点
            parent: 父节点（用于确定搜索方向）
            goal: 目标位置
            min_height: 最小飞行高度
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象
            vertical_look_steps: 向上查看的最大步数

        Returns:
            Optional[GridNode3D]:
            如果找到跳点，返回跳点节点
            如果未找到跳点，返回None
        """
        if not current_node:
            return None

        # 快速目标检查
        if (current_node.y, current_node.x) == (goal[0], goal[1]):
            return current_node  # 路径成本为0

        # 如果是起始节点，直接返回
        if not parent_node:
            return current_node  # 路径成本为0

        # 计算移动方向
        offset_y = current_node.y - parent_node.y
        dy = jump_step * offset_y // abs(offset_y) if offset_y != 0 else 0
        offset_x = current_node.x - parent_node.x
        dx = jump_step * offset_x // abs(offset_x) if offset_x != 0 else 0
        dz = 0

        # 如果是垂直方向的移动，在到达一定高度后应该恢复水平移动
        # 如果是向上移动，则在下一步尝试恢复水平移动
        # if dz > 0 and (dy != 0 or dx != 0):
        #     # 将垂直方向的移动重置为0，下一步将水平移动
        #     dz = 0

        if dx != 0 and dy != 0 and dz == 0:  # XY平面对角移动
            per_path_cost = 1.414 * jump_step  # 对角移动成本为1.414
        else:
            per_path_cost = 1.0 * jump_step  # 直线移动成本为1

        # direction = (dy, dx, dz)

        # 初始化路径和路径成本
        intermediate_path = [current_node]
        # path_cost = 0.0  # 初始化路径成本

        # step_dy = dy * jump_step
        # step_dx = dx * jump_step
        # step_dz = dz * jump_step
        step_cruise_speed_t = self.cruise_speed_t * jump_step

        # 沿着同一方向继续移动
        next_y = current_node.y + dy
        next_x = current_node.x + dx
        next_z = current_node.z + dz

        next_t = current_node.t + step_cruise_speed_t
        next_g = current_node.g + per_path_cost

        next_node = GridNode3D(next_y, next_x, next_z, t=next_t, g=next_g)
        next_node.jump_step = jump_step
        next_node.parent = current_node

        steps = jump_step

        while True:
            # 检查是否可达
            is_valid, error = self._is_valid_position(
                (next_node.y, next_node.x, next_node.z),
                next_node.t,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )

            # 如果下一个位置不可达，尝试向上查看
            if not is_valid and dz == 0:  # 只在水平移动时尝试向上
                if "超出地图范围" in error:
                    current_node.need_sight = False if steps == 0 else True
                    return current_node

                obstacle_types = self.map.obstacle_manager.get_type_at_position(
                    (next_node.y, next_node.x, next_node.z)
                )
                if obstacle_types:  # 遇到固定障碍物或禁飞区
                    current_node.need_sight = False
                    return current_node

                found_vertical_path = False
                # 尝试向上查看几步
                for step in range(1, vertical_look_steps + 1, 2):
                    vertical_pos = (
                        current_node.y,
                        current_node.x,
                        current_node.z + step,
                    )

                    # 检查是否超出地图范围
                    if (
                        vertical_pos[0] >= self.map.height
                        or vertical_pos[1] >= self.map.width
                        or vertical_pos[2] >= self.map.depth
                        or vertical_pos[0] < 0
                        or vertical_pos[1] < 0
                        or vertical_pos[2] < 0
                    ):
                        current_node.need_sight = True
                        return current_node

                    up_time = current_node.t + self.cruise_speed_t * step

                    # 检查垂直位置是否可达
                    vertical_valid, _ = self._is_valid_position(
                        vertical_pos,
                        up_time,
                        min_height,
                        agent_id,
                        constraints,
                        occupancy_map,
                    )

                    if vertical_valid:
                        # 创建垂直上升节点
                        up_node = GridNode3D(
                            vertical_pos[0],
                            vertical_pos[1],
                            vertical_pos[2],
                            up_time,
                        )
                        up_node.parent = intermediate_path[-1]

                        # 垂直移动成本为1.0
                        up_node.g = intermediate_path[-1].g + 1.0 * step

                        intermediate_path.append(up_node)

                        # 检查水平移动节点是否可行
                        horizontal_pos = (next_node.y, next_node.x, next_node.z + step)
                        horizontal_time = up_node.t + step_cruise_speed_t

                        horizontal_valid, _ = self._is_valid_position(
                            horizontal_pos,
                            horizontal_time,
                            min_height,
                            agent_id,
                            constraints,
                            occupancy_map,
                        )

                        if not horizontal_valid:
                            # 如果水平移动不可行，尝试下一个高度
                            continue

                        # 创建水平移动节点
                        next_node = GridNode3D(
                            horizontal_pos[0],
                            horizontal_pos[1],
                            horizontal_pos[2],
                            horizontal_time,
                        )
                        next_node.parent = up_node
                        next_node.g = up_node.g + per_path_cost
                        next_node.jump_step = jump_step

                        intermediate_path.append(next_node)

                        # 重要：在向上移动后，将dz重置为0，以确保后续移动是水平的
                        dz = 0

                        found_vertical_path = True
                        break

                # 如果没有找到垂直路径，结束搜索
                if not found_vertical_path:
                    current_node.need_sight = False
                    return current_node

            else:
                # 正常情况，创建下一个节点
                # 注意：我们已经在前面检查了next_pos的可行性，所以这里可以直接创建节点
                intermediate_path.append(next_node)

            # 检查是否到达目标
            if (next_node.y, next_node.x) == (goal[0], goal[1]):
                return next_node

            # 更新current变量为当前节点
            current_node = intermediate_path[-1]

            # 更新位置和时间
            next_y = current_node.y + dy
            next_x = current_node.x + dx
            next_z = current_node.z + dz
            next_t = current_node.t + step_cruise_speed_t
            next_g = current_node.g + per_path_cost

            next_node = GridNode3D(
                next_y,
                next_x,
                next_z,
                t=next_t,
                g=next_g,
            )
            next_node.parent = current_node

            steps += 1 * jump_step

            # 检查是否还在向目标靠近
            if (
                self._octile_distance(
                    (current_node.y, current_node.x, current_node.z), goal
                )
                - self._octile_distance((next_y, next_x, next_z), goal)
                <= 0
                or steps > settings.settings.pathplanning.max_steps  # Corrected access
            ):
                return current_node

    def euclidean_distance_squared(
        self, pos1: Tuple[int, int, int], pos2: Tuple[int, int, int]
    ) -> float:
        """计算两点间的欧几里得距离的平方（避免开方运算提高性能）

        Args:
            pos1: 第一个点的坐标 (y, x, z)
            pos2: 第二个点的坐标 (y, x, z)

        Returns:
            float: 欧几里得距离的平方
        """
        return (
            (pos1[0] - pos2[0]) ** 2
            + (pos1[1] - pos2[1]) ** 2
            + (pos1[2] - pos2[2]) ** 2
        )

    def has_line_of_sight(
        self,
        pos1_node: GridNode3D,
        pos2: Tuple[int, int, int],
        t: Optional[int] = None,
        min_height: int = 0,
        agent_id: str = "0",
        constraints: Optional[List] = None,
        occupancy_map=None,
    ):
        """检查两点之间是否有直接视线（考虑时间约束和占用图）

        Args:
            pos1_node: 起始位置node类型
            pos2: 目标位置 (y, x, z)
            t: 当前时间戳（如果指定）
            min_height: 最小巡航高度
            agent_id: 智能体ID
            constraints: 时空约束列表
            occupancy_map: 占用图

        Returns:
            Tuple[bool, List[Tuple[Tuple[int, int, int], int]]]:
            - bool: 如果两点之间有直接视线且满足约束条件返回True，否则返回False
            - List: 路径上的所有点及其对应的时间戳列表，格式为[(pos, t), ...]
        """
        # 如果点过于接近，不需要视线检查
        # if self.euclidean_distance_squared(pos1, pos2) < 25:
        #     return True, [(pos1, t if t is not None else 0)]

        # 计算方向向量和步数
        dy = pos2[0] - pos1_node.y
        dx = pos2[1] - pos1_node.x
        # dz = pos2[2] - pos1_node.z
        dz = 0
        steps = max(abs(dy), abs(dx), abs(dz))

        if steps == 0:
            return True, [pos1_node]

        # 计算每步的增量
        sy = dy / steps
        sx = dx / steps
        sz = dz / steps

        # 检查路径上的每个点
        current_t = t if t is not None else 0
        path_points = []  # 存储路径上的点和时间戳
        parent_node = pos1_node
        current_node = pos1_node
        path_points.append(parent_node)
        last_pos = None  # 记录上一个点的位置

        for i in range(1, steps + 1, pos1_node.jump_step):
            # 计算当前检查点的坐标（四舍五入到最近的网格点）
            y_float = pos1_node.y + sy * i
            x_float = pos1_node.x + sx * i
            z_float = pos1_node.z + sz * i
            y = int(y_float + 0.5)
            x = int(x_float + 0.5)
            z = int(z_float + 0.5)
            # y = int(pos1[0] + sy * i + 0.5)
            # x = int(pos1[1] + sx * i + 0.5)
            # z = int(pos1[2] + sz * i + 0.5)
            current_pos = (y, x, z)

            # 如果当前点与上一个点相同，跳过这个点但保持时间不变
            if last_pos == current_pos:
                continue

            current_t += self.cruise_speed_t  # 只有在点不重复时才增加时间

            is_valid, error = self._is_valid_position(
                current_pos,
                current_t,  # 使用当前时间
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )

            if not is_valid:

                return False, [
                    GridNode3D(
                        int(current_node.y),
                        int(current_node.x),
                        int(current_node.z),
                        current_t,
                    )
                ]

            # 记录有效点及其时间戳
            # path_points.append(GridNode3D(y, x, z, current_t))
            current_node = GridNode3D(y_float, x_float, z_float, current_t)
            current_node.parent = parent_node
            path_points.append(current_node)

            parent_node = current_node
            last_pos = current_pos  # 更新上一个点的位置

        current_node = GridNode3D(pos2[0], pos2[1], pos1_node.z, current_t)
        current_node.parent = parent_node
        path_points.append(current_node)
        return True, path_points

    def _find_cruise_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,
    ):
        """使用优化的跳点搜索进行巡航路径规划

        在固定高度层执行路径搜索，这是整个路径规划中最复杂的阶段。
        使用A*算法的框架，但结合了跳点搜索(JPS)来优化搜索效率。

        Args:
            start: 起始位置坐标 (y, x, z)
            goal: 目标位置坐标 (y, x, z)
            min_height: 最小巡航高度
            agent_id: 智能体ID
            start_time: 开始时间（如果指定）
            occupancy_map: 占用图对象
            constraints: 时空约束列表

        Returns:
            Tuple[Optional[List[GridNode3D]], Optional[str]]:
            - 如果成功，返回(路径节点列表, None)
            - 如果失败，返回(None, 错误信息)
        """
        # 起点到终点的直线距离
        distance = self._euclidean_distance(start, goal)
        # 检测终点是否可达
        is_valid, _ = self._is_valid_position(
            goal,
            (
                start_time + self.cruise_speed_t * distance
                if start_time is not None
                else 0
            ),
            min_height,
            agent_id,
            constraints,
            occupancy_map,
        )
        if not is_valid:
            return None, "巡航终点被占用，无法到达", True

        open_list = []  # 优先队列，存储待探索的节点
        closed = set()  # 已探索的节点集合
        counter = 0  # 用于打破f值相等时的平局
        # path_nodes = {}  # 存储路径节点，避免重复创建
        # intermediate_paths = {}  # 存储节点间的完整中间路径

        # 启发式函数权重，控制算法的贪婪程度
        # 设置大于1的值会使算法更倾向于探索离目标更近的路径
        # 这可能会牌牲一些路径的最优性，但可以提高搜索效率
        heuristic_weight = 2.0  # 可以根据需要调整这个值

        def heuristic(pos: Tuple[int, int, int]) -> float:
            """加权启发式函数：使用八方向距离，并应用权重以提高离目标近的节点的优先级"""
            # 计算基本启发式值
            base_h = self._octile_distance(pos, goal)
            # 应用权重
            return heuristic_weight * base_h

            # 其他可选的启发式函数
            # return heuristic_weight * self._manhattan_distance(pos, goal)  # 加权曼哈顿距离
            # return heuristic_weight * self._euclidean_distance(pos, goal)  # 加权欧几里得距离

        # 初始化起始节点
        # t = start_time if start_time is not None else 0
        start_node = GridNode3D(start[0], start[1], start[2], start_time)
        # start = (start_node.y, start_node.x, start_node.z)
        start_node.g = 0
        start_node.h = heuristic(start)
        start_node.f = start_node.h
        # start_node.parent = None

        if start_node.h < 25:
            start_node.jump_step = 1

        # 将起始节点加入开启列表
        heapq.heappush(open_list, (start_node.f, counter, start_node))
        # path_nodes[start] = start_node

        # 搜索主循环
        while open_list:
            # 取出f值最小的节点
            current = heapq.heappop(open_list)[2]
            current_pos = (current.y, current.x, current.z)

            # 如果节点已经探索过，跳过
            if current_pos in closed:
                continue

            # 首先检查当前点和终点之间是否有直接视线
            if current.need_sight:
                has_los, los_points = self.has_line_of_sight(
                    current,
                    goal,
                    start_time,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                )
                if has_los:
                    # 重建完整路径
                    path = []
                    node = los_points[-1]
                    while node:
                        path.append(node)

                        # 检查每个点和上一个点之间的距离
                        # if node.parent:
                        #     parent_pos = (node.parent.y, node.parent.x, node.parent.z)
                        #     node_pos = (node.y, node.x, node.z)
                        #     distance_squared = self.euclidean_distance_squared(
                        #         parent_pos, node_pos
                        #     )
                        #
                        #     # 如果距离大于40，直接返回None，表示路径不合法
                        #     if distance_squared > 40**2:
                        #         return None, "路径中点与点之间的距离超过40", False

                        node = node.parent
                    return path[::-1], None, False

            # 将当前节点加入已探索集合
            closed.add(current_pos)

            # 获取并处理邻居节点
            neighbors = self._get_neighbors(
                current,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )
            # 把所有邻域点加入next_list
            next_list = []  # 临时存储节点
            for next_points in neighbors:
                if next_points[-1] in closed:
                    continue
                pre_node = current
                for next_point in next_points:
                    next_t = current.t + self.cruise_speed_t
                    next_node = GridNode3D(
                        next_point[0], next_point[1], next_point[2], t=next_t
                    )
                    next_node.parent = pre_node

                    # 计算每一个领域点的g,h,f值
                    next_node.g = current.g + 1
                    next_node.h = heuristic(next_point)
                    next_node.f = next_node.g + next_node.h

                    pre_node = next_node

                heapq.heappush(next_list, (next_node.h, next_node))

            jump_step = current.jump_step

            neighbor_count = 0
            while next_list:
                if neighbor_count >= 3:
                    break

                next_node = heapq.heappop(next_list)[1]
                neighbor_count += 1

                # 尝试跳点搜索，获取跳点和中间路径
                jump_node = self._jump(
                    next_node,
                    current,
                    goal,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                    jump_step=jump_step,
                    vertical_look_steps=5,  # 向上查看的步数
                )

                if not jump_node:
                    continue

                jump_node.h = heuristic((jump_node.y, jump_node.x, jump_node.z))
                jump_node.f = jump_node.g + jump_node.h

                if jump_node.h < 20:
                    jump_node.jump_step = 1

                # 保存中间路径，并确保路径中所有节点的父节点关系正确
                # for i in range(1, len(intermediate_path)):
                #     intermediate_path[i].parent = intermediate_path[i - 1]
                # intermediate_paths[(jump_node, current_pos)] = intermediate_path

                # 更新路径节点字典
                # path_nodes[jump_pos] = jump_node

                # 将跳点加入开启列表
                counter += 1
                heapq.heappush(open_list, (jump_node.f, counter, jump_node))

        return None, "巡航阶段无法找到路径", False

    def _vertical_takeoff(
        self,
        start: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,  # 主要是针对多无人机同时起飞情况
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """执行垂直起飞阶段的路径规划

        从起点垂直上升到指定的最小巡航高度，这是整个路径规划的第一阶段。
        起飞阶段会忽略最小高度限制，因为无人机需要从地面逐渐上升。

        Args:
            start: 起飞点坐标 (y, x, z)
            min_height: 最小巡航高度
            agent_id: 智能体ID，用于多机协调
            start_time: 开始起飞的时刻（如果指定）
            occupancy_map: 占用图，用于避免与其他无人机碰撞
            constraints: 时空约束列表，用于多机协调

        Returns:
            Tuple[Optional[List[GridNode3D]], Optional[str]]:
            - 如果成功，返回(路径节点列表, None)
            - 如果失败，返回(None, 错误信息)
        """
        path = []
        current_t = start_time if start_time is not None else 0

        step_size = 5  # 每次跳跃的高度单位
        heights = list(range(start[2], min_height + 1, step_size))
        # 确保包含最后一个元素（最小巡航高度）
        if min_height not in heights:
            heights.append(min_height)

        # 从起点垂直上升到最小巡航高度
        for z in heights:
            pos = (start[0], start[1], z)

            # 检查约束和碰撞，在起飞阶段忽略最小高度限制
            is_valid, error = self._is_valid_position(
                pos,
                current_t,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
                ignore_min_height=True,
            )
            if not is_valid:
                real_z = self.map.grid_converter.relative_to_geo(0, 0, z)["alt"]
                return None, f"垂直爬升阶段在高度 {real_z} {error}"

            # 创建新的路径节点并维护父子关系
            node = GridNode3D(start[0], start[1], z, current_t)
            # if path:  # 将新节点与前一个节点链接
            #     node.parent = path[-1]
            path.append(node)
            current_t += self.takeoff_speed_t

        return path, None

    def _vertical_landing(
        self,
        last_cruise_node: GridNode3D,
        goal: Tuple[int, int, int],
        agent_id: str,
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """执行垂直降落阶段的路径规划

        从巡航高度垂直下降到目标点，这是整个路径规划的最后阶段。
        降落阶段不需要考虑最小高度限制。

        Args:
            last_cruise_node: 巡航阶段的最后一个节点
            goal: 目标降落点坐标 (y, x, z)
            agent_id: 智能体ID，用于多机协调
            occupancy_map: 占用图，用于避免与其他无人机碰撞
            constraints: 时空约束列表，用于多机协调

        Returns:
            Tuple[Optional[List[GridNode3D]], Optional[str]]:
            - 如果成功，返回(路径节点列表, None)
            - 如果失败，返回(None, 错误信息)
        """
        path = []
        current_t = last_cruise_node.t + self.landing_speed_t

        step_size = -5  # 每次跳跃的高度单位
        heights = list(range(last_cruise_node.z - 1, goal[2] - 1, step_size))
        # 确保包含最后一个元素（最小巡航高度）
        if goal[2] not in heights:
            heights.append(goal[2])

        # 从巡航高度垂直下降到目标点
        for z in heights:
            pos = (goal[0], goal[1], z)

            # 检查约束和碰撞，降落阶段不需要考虑最小高度限制
            is_valid, error = self._is_valid_position(
                pos,
                current_t,
                0,  # 降落阶段不需要考虑最小高度限制
                agent_id,
                constraints,
                occupancy_map,
            )
            if not is_valid:
                real_z = self.map.grid_converter.relative_to_geo(0, 0, z)
                return None, f"降落阶段在高度 {real_z} {error}"

            # 创建新的路径节点并维护父子关系
            node = GridNode3D(goal[0], goal[1], z, current_t)
            # if path:  # 将新节点与前一个节点链接
            #     node.parent = path[-1]
            # elif last_cruise_node:  # 第一个降落节点的父节点是巡航阶段的最后一个节点
            #     node.parent = last_cruise_node
            path.append(node)
            current_t += self.landing_speed_t

        return path, None

    def find_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int = 0,
        agent_id: str = "0",
        start_time: Optional[int] = None,
        occupancy_map=None,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """主路径规划方法，将路径规划分为三个阶段

        完整的路径规划包括三个阶段：
        1. 垂直起飞：从起点上升到最小巡航高度
        2. 巡航：在固定高度层执行路径搜索
        3. 垂直降落：从巡航高度下降到目标点

        Args:
            start: 起点坐标 (y, x, z)
            goal: 终点坐标 (y, x, z)
            min_height: 最小巡航高度，默认为0
            agent_id: 智能体标识，默认为"0"
            start_time: 起始时间（如果指定），默认为None
            occupancy_map: 用于多机避碰的占用图
            constraints: 时空约束列表，用于多机协调

        Returns:
            Tuple[Optional[List[GridNode3D]], Optional[str]]:
            - 如果成功，返回(完整路径节点列表, None)
            - 如果失败，返回(None, 错误信息)
        """
        # 1. 检查是否需要垂直起飞
        if start[2] >= min_height:
            # 起始高度已经满足最小高度要求，直接从当前高度开始巡航
            start_node = GridNode3D(
                start[0],
                start[1],
                start[2],
                start_time if start_time is not None else 0,
            )
            takeoff_path = [start_node]
        else:
            # 需要垂直起飞
            takeoff_path, error = self._vertical_takeoff(
                start, min_height, agent_id, start_time, occupancy_map, constraints
            )
            if not takeoff_path:
                return None, f"起飞阶段失败: {error}"

        # 2. 巡航阶段
        cruise_goal = (goal[0], goal[1], min_height)  # 在巡航高度的投影点
        cruise_path, error, is_linesight = self._find_cruise_path(
            (takeoff_path[-1].y, takeoff_path[-1].x, takeoff_path[-1].z),
            cruise_goal,
            min_height,
            agent_id,
            takeoff_path[-1].t,  # 使用起飞结束时刻作为巡航开始时间
            occupancy_map,
            constraints,
        )
        if not cruise_path:
            return None, f"巡航阶段失败: {error}"

        # 确保巡航路径与起飞路径正确连接
        # if cruise_path and takeoff_path:
        #     cruise_path[0].parent = takeoff_path[-1]

        # 3. 垂直降落阶段
        landing_path, error = self._vertical_landing(
            cruise_path[-1], goal, agent_id, occupancy_map, constraints
        )
        if error is not None:
            return None, f"降落阶段失败: {error}"

        # 先确定拐点路径和拐点索引
        if is_linesight:
            cruise_turn_path = cruise_path[:1] + cruise_path[-1:]
            cruise_turn_indices = []  # 直线视线路径没有中间拐点
        else:
            cruise_turn_path, cruise_turn_indices = self.extract_turning_points(
                cruise_path
            )

        # 如果需要平滑，对巡航路径进行平滑处理
        if settings.settings.pathplanning.need_smooth:  # Corrected access
            # 使用拐点索引进行平滑，提高效率，同时获取平滑后的完整路径和拐点路径
            cruise_path, cruise_turn_path = self.moving_average_smooth(
                cruise_path,
                cruise_turn_indices,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
                settings.settings.pathplanning.smoothness,  # Corrected access
            )

        # 合并三个阶段的路径，形成完整路径
        complete_path = takeoff_path[:-1] + cruise_path + landing_path
        complete_turn_path = takeoff_path[:1] + cruise_turn_path + landing_path[-1:]

        return (complete_path, complete_turn_path), None

    def extract_turning_points(
        self, path: List[GridNode3D]
    ) -> Tuple[List[GridNode3D], List[int]]:
        """从完整路径中提取拐点（方向变化的点）及其索引

        分析路径中的点，只保留起点、终点和方向发生变化的点，
        从而得到一个简化的路径，只包含关键的拐点。
        同时返回拐点在原始路径中的索引，但不包括起点和终点的索引。

        Args:
            path: 完整的路径点列表

        Returns:
            Tuple[List[GridNode3D], List[int]]:
                - 只包含拐点的路径列表（包括起点和终点）
                - 拐点在原始路径中的索引列表（不包括起点和终点）
        """
        if not path or len(path) <= 2:
            return path, []  # 如果路径为空或只有两个点，直接返回原路径和空索引列表

        # 初始化拐点列表和索引列表
        turning_points = [path[0]]  # 始终包含起点
        turning_indices = []  # 不包含起点和终点的索引

        # 遍历路径中的点，检测方向变化
        for i in range(1, len(path) - 1):
            prev_node = path[i - 1]
            curr_node = path[i]
            next_node = path[i + 1]

            # 计算当前段和下一段的方向向量
            curr_dir_vec = (
                curr_node.y - prev_node.y,
                curr_node.x - prev_node.x,
                curr_node.z - prev_node.z,
            )
            # 避免除以零
            curr_norm = np.linalg.norm(curr_dir_vec)
            if curr_norm == 0:
                continue  # Skip if the current segment has zero length
            curr_dir = np.array(curr_dir_vec) / curr_norm

            next_dir_vec = (
                next_node.y - curr_node.y,
                next_node.x - curr_node.x,
                next_node.z - curr_node.z,
            )
            # 避免除以零
            next_norm = np.linalg.norm(next_dir_vec)
            if next_norm == 0:
                continue  # Skip if the next segment has zero length
            next_dir = np.array(next_dir_vec) / next_norm

            # 如果方向发生变化，则当前点是拐点
            # 使用 np.any() 来比较 NumPy 数组
            if np.any(curr_dir != next_dir):
                # 计算距离的平方来判断是否足够不同 (替代角度检查)
                dis2 = np.sum((curr_dir - next_dir) ** 2)  # Squared Euclidean distance

                # 使用一个小的阈值来判断方向是否显著不同
                if dis2 > 0.02:  # 阈值可以调整
                    turning_points.append(curr_node)
                    turning_indices.append(i)  # 记录拐点索引

        # 添加终点
        turning_points.append(path[-1])  # 始终包含终点

        return turning_points, turning_indices

    def moving_average_smooth(
        self,
        path: List[GridNode3D],
        turning_indices: List[int],
        min_height,
        agent_id: str,
        constraints,
        occupancy_map,
        smoothness: float,
    ) -> Tuple[List[GridNode3D], List[GridNode3D]]:
        """
        使用移动平均法平滑路径，仅对拐点附近的区域进行平滑处理
        同时返回完整平滑路径和平滑后的拐点路径

        这是一种优化的平滑方法，只对路径中的关键区域（拐点附近）进行平滑，
        从而提高效率同时保持路径质量

        Args:
            path: 原始路径节点列表
            turning_indices: 拐点在原始路径中的索引列表（不包括起点和终点）
            min_height: 最小巡航高度
            agent_id: 智能体ID
            constraints: 约束条件
            occupancy_map: 占用图
            smoothness: 平滑程度

        Returns:
            Tuple[List[GridNode3D], List[GridNode3D]]:
                - 平滑后的完整路径节点列表
                - 平滑后的拐点路径节点列表（只包含起点、终点和拐点）
        """
        if len(path) < 3:
            return path.copy(), path.copy()

        # 计算窗口大小，基于平滑程度
        window_size = max(3, int(smoothness))
        if window_size % 2 == 0:
            window_size += 1  # 确保窗口大小为奇数

        half_window = window_size // 2

        # 创建一个集合，用于标记需要平滑的点的索引
        indices_to_smooth = set()

        # 对每个拐点，标记其周围的点用于平滑
        for turn_idx in turning_indices:
            # 标记拐点周围的点（窗口大小范围内）
            start_idx = max(1, turn_idx - half_window)  # 不包括起点
            end_idx = min(len(path) - 2, turn_idx + half_window)  # 不包括终点

            # 将这些点的索引添加到集合中
            for idx in range(start_idx, end_idx + 1):
                indices_to_smooth.add(idx)

        # 如果没有需要平滑的点，直接返回原始路径
        if not indices_to_smooth:
            # 创建拐点路径（起点、拐点和终点）
            turning_path = [path[0]]  # 起点
            for idx in turning_indices:
                turning_path.append(path[idx])
            turning_path.append(path[-1])  # 终点
            return path.copy(), turning_path

        # 初始化结果列表
        smoothed_path = [path[0]]  # 保留起点

        # 创建平滑后的拐点路径，先添加起点
        smoothed_turning_path = [path[0]]  # 起点

        # 创建一个集合，用于标记哪些索引是拐点
        turning_indices_set = set(turning_indices)

        # 对中间点进行平滑，只处理需要平滑的点
        for i in range(1, len(path) - 1):
            curr_node = path[i]
            is_turning_point = i in turning_indices_set

            # 如果当前点不在需要平滑的索引集合中，直接添加原始点
            if i not in indices_to_smooth:
                smoothed_path.append(curr_node)
                # 如果是拐点，添加到拐点路径
                if is_turning_point:
                    smoothed_turning_path.append(curr_node)
                continue

            # 检查当前点是否涉及高度变化
            prev_node = path[i - 1]
            next_node = path[i + 1]
            if curr_node.z != prev_node.z or curr_node.z != next_node.z:
                # 如果Z坐标发生变化，则不平滑，直接使用原始点
                smoothed_path.append(curr_node)
                # 如果是拐点，添加到拐点路径
                if is_turning_point:
                    smoothed_turning_path.append(curr_node)
                continue

            # 仅在高度不变时执行平滑
            # 确定对称窗口范围
            left_available = i
            right_available = len(path) - 1 - i
            actual_half_window = min(half_window, left_available, right_available)

            start_idx = i - actual_half_window
            end_idx = i + actual_half_window + 1
            window = path[start_idx:end_idx]

            # 确保窗口大小至少为1，避免空窗口
            if not window:
                smoothed_path.append(curr_node)  # 如果窗口为空，则使用原始点
                # 如果是拐点，添加到拐点路径
                if is_turning_point:
                    smoothed_turning_path.append(curr_node)
                continue

            # 计算窗口内点的加权平均
            weights = np.exp(-0.5 * np.square(np.linspace(-1, 1, len(window))))
            weights = weights / np.sum(weights)

            y_sum = sum(node.y * weight for node, weight in zip(window, weights))
            x_sum = sum(node.x * weight for node, weight in zip(window, weights))
            # z_sum = sum(node.z * weight for node, weight in zip(window, weights)) # 不对Z坐标进行平滑
            original_z = curr_node.z  # 保留原始Z坐标

            is_valid, _ = self._is_valid_position(
                (round(y_sum), round(x_sum), original_z),  # 使用原始Z坐标进行检查
                curr_node.t,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )

            if not is_valid:
                smoothed_path.append(curr_node)  # 如果发生碰撞，使用原始点
                # 如果是拐点，添加到拐点路径
                if is_turning_point:
                    smoothed_turning_path.append(curr_node)
                continue

            # 创建新节点，使用平滑后的Y/X和原始的Z
            new_node = GridNode3D(round(y_sum), round(x_sum), original_z, curr_node.t)

            # 添加到平滑路径
            smoothed_path.append(new_node)

            # 如果是拐点，添加到拐点路径
            if is_turning_point:
                smoothed_turning_path.append(new_node)

        # 添加终点
        smoothed_path.append(path[-1])
        smoothed_turning_path.append(path[-1])  # 终点

        return smoothed_path, smoothed_turning_path
