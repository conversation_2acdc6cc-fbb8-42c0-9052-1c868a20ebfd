import mysql.connector
import time
from typing import List, Tuple, Dict, Optional
from ..node_3d import GridNode3D
from ..map.map_handler_3d import Map3D
from ..map.occupancy_map import OccupancyMap
from ...config import settings  # Corrected import
from ...utils.logging import get_logger

logger = get_logger(__name__)


class RouteValidator:
    # 类变量，用于管理持久连接
    db_conn = None
    db_conn_last_active = None
    DB_CONN_TIMEOUT = 300  # 5分钟超时

    def __init__(self, map3d: Map3D, occupancy_map=None):
        """初始化路径验证器

        Args:
            map3d: Map3D实例
            occupancy_map: 可选，外部传入的占用图实例
        """
        self.map = map3d
        self.grid_converter = self.map.grid_converter
        self.occupancy_map = (
            occupancy_map
            if occupancy_map
            else OccupancyMap(
                (self.map.height, self.map.width, self.map.depth), time_buffer=1
            )
        )
        # 使用settings中的数据库配置 (已合并)
        self.db_config = {
            "host": settings.settings.database.host,  # Corrected access
            "port": settings.settings.database.port,  # Corrected access
            "user": settings.settings.database.user,  # Corrected access
            "password": settings.settings.database.password,  # Corrected access
            "database": settings.settings.database.database,  # Corrected access
            # 添加连接池配置
            "pool_size": 10,  # 增加连接池大小到10
            "pool_name": "route_validator_pool",
            # 添加连接配置
            "autocommit": True,  # 自动提交
            "buffered": True,  # 缓冲查询结果
            # 连接重试设置
            "get_warnings": True,
            "raise_on_warnings": True,
            # 保活设置
            "connection_timeout": 60,  # 增加连接超时时间
            "use_pure": True,  # 使用纯Python实现，更稳定
            # 添加连接池设置
            "pool_reset_session": True,  # 重置会话状态
        }

    def _init_db_conn(self):
        """初始化数据库连接"""
        if not RouteValidator.db_conn:
            try:
                RouteValidator.db_conn = mysql.connector.connect(**self.db_config)
                RouteValidator.db_conn_last_active = time.time()
                logger.info("固定航路数据库连接初始化成功")
            except Exception as e:
                logger.error(f"固定航路数据库连接初始化失败: {str(e)}")
                RouteValidator.db_conn = None

    def _check_db_conn(self):
        """检查数据库连接状态，必要时重新连接"""
        if not RouteValidator.db_conn:
            logger.debug("固定航路数据库连接不存在或已断开，尝试初始化")
            self._init_db_conn()
            return bool(RouteValidator.db_conn)

        # 检查连接是否超时
        # if time.time() - RouteValidator.db_conn_last_active > self.DB_CONN_TIMEOUT:
        #     try:
        #         # 先尝试ping恢复连接，最多3次，每次间隔1秒
        #         RouteValidator.db_conn.ping(reconnect=True, attempts=3, delay=1)
        #         RouteValidator.db_conn_last_active = time.time()
        #         logger.debug("固定航路数据库连接保持活跃成功")
        #         return True
        #     except Exception as e:
        #         logger.warning(f"固定航路数据库连接ping失败: {str(e)}，尝试重新初始化")
        #         try:
        #             RouteValidator.db_conn.close()
        #         except:
        #             pass
        #         self._init_db_conn()
        #         return bool(RouteValidator.db_conn)

        return True

    def _fetch_route_points(
        self, route_id: str, batch_size: int = 1000
    ) -> List[Tuple[float, float, float, int]]:
        """从数据库获取航线的坐标点

        Args:
            route_id: 航线ID（flight_route_manage_id）
            batch_size: 批处理大小，默认1000条记录

        Returns:
            List[Tuple[float, float, float, int]]: 航线点列表，每个点包含(经度, 纬度, 海拔, 时间戳)
        """
        if not self._check_db_conn():
            logger.error("固定航线数据库连接不可用")
            return None

        cursor = None
        try:
            cursor = RouteValidator.db_conn.cursor()

            # 首先获取记录总数
            count_query = (
                "SELECT COUNT(*) FROM route_node_manage WHERE flight_route_id = %s"
            )
            cursor.execute(count_query, (route_id,))
            total_count = cursor.fetchone()[0]

            if total_count == 0:
                error_msg = f"未找到航线ID {route_id} 的坐标点"
                logger.error(error_msg)
                return None, None, error_msg, None, False

            logger.info(f"航线ID {route_id} 共有 {total_count} 个坐标点")

            # 分批查询所有路径点，按node_index排序
            all_points = []
            for offset in range(0, total_count, batch_size):
                query = """
                    SELECT longitude, latitude, height, node_index
                    FROM route_node_manage
                    WHERE flight_route_id = %s
                    ORDER BY node_index
                    LIMIT %s OFFSET %s
                """
                cursor.execute(query, (route_id, batch_size, offset))
                batch_points = cursor.fetchall()
                all_points.extend(batch_points)
                logger.debug(
                    f"已获取批次: {offset//batch_size + 1}/{(total_count-1)//batch_size + 1}, 进度: {min(offset+batch_size, total_count)}/{total_count}"
                )

            # 更新最后活动时间
            RouteValidator.db_conn_last_active = time.time()

            # 使用node_index作为相对时间戳（每个点间隔1秒）
            # 返回格式：(经度, 纬度, 海拔, 时间戳)
            return [(lon, lat, height, idx) for lon, lat, height, idx in all_points]

        except Exception as e:
            logger.error(f"获取固定航线点失败: {str(e)}")
            return None
        finally:
            if cursor:
                cursor.close()

    def _interpolate_points(
        self, p1: Tuple[float, float, float], p2: Tuple[float, float, float]
    ) -> List[Tuple[int, int, int]]:
        """在两个路径点之间进行线性插值

        Args:
            p1: 起点坐标 (经度, 纬度, 高度)
            p2: 终点坐标 (经度, 纬度, 高度)

        Returns:
            List[Tuple[int, int, int]]: 插值后的网格坐标点列表 (y, x, z)
        """
        # 将经纬度转换为网格坐标
        y1, x1, z1 = self.grid_converter.geo_to_relative(p1[1], p1[0], p1[2])
        y2, x2, z2 = self.grid_converter.geo_to_relative(p2[1], p2[0], p2[2])

        # 检查起点和终点是否有效
        if not self.map.traversable_map[y1, x1, z1]:
            conflict_types = self.map.obstacle_manager.get_type_at_position(
                (y1, x1, z1)
            )
            # error_msg = f"点 ({y1}, {x1}, {z1}) 与障碍物类型 {conflict_types} 发生碰撞"
            # logger.error(error_msg)
            # return [], None, error_msg, None, False
            raise Exception(
                f"起点 ({y1}, {x1}, {z1}) 与障碍物类型 {conflict_types} 发生碰撞"
            )

        if not self.map.traversable_map[y2, x2, z2]:
            conflict_types = self.map.obstacle_manager.get_type_at_position(
                (y2, x2, z2)
            )
            # error_msg = f"点 ({y2}, {x2}, {z2}) 与障碍物类型 {conflict_types} 发生碰撞"
            # logger.error(error_msg)
            # return [], None, error_msg, None, False
            raise Exception(
                f"终点 ({y2}, {x2}, {z2}) 与障碍物类型 {conflict_types} 发生碰撞"
            )

        # 计算网格坐标差异
        dy = y2 - y1
        dx = x2 - x1
        dz = z2 - z1

        # 计算需要插入的点数（使用曼哈顿距离）
        distance = abs(dx) + abs(dy) + abs(dz)
        if distance <= 1:
            return [(y1, x1, z1)]  # 如果点足够近，只返回起点

        # 生成插值点
        points = []
        steps = int(distance)
        for i in range(steps):
            # 计算当前位置的网格坐标
            ratio = i / steps
            grid_y = int(y1 + dy * ratio)
            grid_x = int(x1 + dx * ratio)
            grid_z = int(z1 + dz * ratio)

            # 检查插值点是否有效
            if not self.map.traversable_map[grid_y, grid_x, grid_z]:
                conflict_types = self.map.obstacle_manager.get_type_at_position(
                    (grid_y, grid_x, grid_z)
                )
                # error_msg = f"路径点 ({grid_y}, {grid_x}, {grid_z}) 与障碍物类型 {conflict_types} 发生碰撞"
                # logger.error(error_msg)
                # return [], None, error_msg, None, False
                raise Exception(
                    f"路径点 ({grid_y}, {grid_x}, {grid_z}) 与障碍物类型 {conflict_types} 发生碰撞"
                )

            points.append((grid_y, grid_x, grid_z))

        return points

    def _convert_to_grid_path(
        self, points: List[Tuple[float, float, float, int]], start_time: int
    ) -> Tuple[List[Tuple[int, int, int]], List[GridNode3D]]:
        """将经纬度坐标点转换为网格坐标路径和节点路径

        Args:
            points: 经纬度坐标点列表，每个点为(经度, 纬度, 海拔, node_index)
            start_time: 起飞时间戳

        Returns:
            Tuple[List[Tuple[int, int, int]], List[GridNode3D]]:
                - 简单网格坐标列表 [(y,x,z), ...]
                - 完整节点列表，用于时空占用检查
        """
        grid_coords = []  # 简单网格坐标列表
        grid_nodes = []  # 完整节点列表
        current_time = start_time

        # 处理所有相邻的路径点对
        for i in range(len(points) - 1):
            p1 = points[i][:3]  # 当前点的经纬度和高度
            p2 = points[i + 1][:3]  # 下一个点的经纬度和高度

            # 在两点之间进行插值，直接得到网格坐标
            interpolated = self._interpolate_points(p1, p2)

            # 创建网格节点
            for y, x, z in interpolated:
                grid_coords.append((y, x, z))
                grid_nodes.append(GridNode3D(y, x, z, current_time))
                current_time += 1

        # 添加最后一个点
        last_point = points[-1]
        y, x, z = self.grid_converter.geo_to_relative(
            last_point[1], last_point[0], last_point[2]
        )
        grid_coords.append((y, x, z))
        grid_nodes.append(GridNode3D(y, x, z, current_time))

        return grid_coords, grid_nodes

    def _check_path_validity(
        self, path: List[GridNode3D], agent_id: str
    ) -> Tuple[bool, Optional[str]]:
        """检查路径是否有效（无碰撞和冲突）

        Args:
            path: 网格坐标路径（已确保不与固定障碍物碰撞）
            agent_id: 智能体ID

        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 失败原因)
        """
        for node in path:
            # 检查动态碰撞
            if self.occupancy_map:
                has_collision, colliding_agent = self.occupancy_map.check_collision(
                    (node.y, node.x, node.z), node.t
                )
                if has_collision and colliding_agent != agent_id:
                    return False, f"在时刻 {node.t} 与智能体 {colliding_agent} 发生碰撞"

        return True, None

    def validate_route(
        self,
        route_id: str,
        flight_id: str,
        start_time: int,
        time_direction: int = 0,
        max_offset: int = 3600,
    ) -> Tuple[
        Optional[List[Tuple[int, int, int]]],
        Optional[List[GridNode3D]],
        Optional[str],
        Optional[int],
        bool,
    ]:
        """验证已有航线是否可行

        Args:
            route_id: 航线ID
            flight_id: 智能体ID
            start_time: 起飞时间戳
            time_direction: 时间搜索方向 (1:向后, -1:向前, 0:双向)
            max_offset: 最大时间偏移量（秒）

        Returns:
            Tuple:
                - List[Tuple[int, int, int]]: 网格坐标列表 [(y,x,z), ...]
                - List[GridNode3D]: 完整网格节点列表
                - str: 错误信息
                - int: 建议的起飞时间
                - bool: 时间是否被修改
        """
        try:
            # 1. 获取航线坐标点
            route_points = self._fetch_route_points(route_id)

            # 2. 转换为网格坐标路径
            try:
                grid_coords, grid_nodes = self._convert_to_grid_path(
                    route_points, start_time
                )
            except Exception as e:
                return None, None, str(e), None, False

            # 3. 检查动态路径冲突
            valid_time, time_modified = self.occupancy_map.find_valid_time(
                grid_nodes, flight_id, start_time, time_direction, max_offset
            )

            if valid_time is not None:
                # 如果找到了可行时间，只在时间被修改时重新生成路径
                if time_modified:
                    # 使用新时间重新生成路径
                    grid_coords, grid_nodes = self._convert_to_grid_path(
                        route_points, valid_time
                    )
                return grid_coords, grid_nodes, None, valid_time, time_modified
            else:
                return None, None, "无法找到合适的起飞时间", None, False

        except Exception as e:
            return None, None, str(e), None, False

    @classmethod
    def close_connections(cls):
        """关闭所有数据库连接"""
        # 关闭数据库连接
        if cls.db_conn:
            try:
                cls.db_conn.close()
                cls.db_conn = None
                logger.info("固定航路数据库连接已关闭")
            except Exception as e:
                logger.error(f"关闭固定航路数据库连接失败: {str(e)}")

        # 最后等待一小段时间，确保资源释放
        time.sleep(0.5)
