import logging
import sys
from pathlib import Path


def setup_logging(log_file: str = "logs/app.log", level: str = "INFO") -> None:
    """
    设置日志配置

    Args:
        log_file: 日志文件路径
        level: 日志级别
    """
    # 创建logs目录
    log_dir = Path(log_file).parent
    log_dir.mkdir(exist_ok=True)

    # 配置日志格式
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 配置文件处理器
    file_handler = logging.FileHandler(log_file, encoding="utf-8")
    file_handler.setFormatter(formatter)

    # 配置控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # 设置 mysql.connector 的日志级别为 WARNING
    logging.getLogger("mysql.connector").setLevel(logging.WARNING)
    # 设置 kafka 的日志级别为 WARNING
    logging.getLogger("kafka").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    获取命名日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(name)
