// 通用类型定义头文件
#pragma once

#include <tuple>
#include <functional> // 为 std::hash 添加此头文件
#include <string>     // 为 std::string 添加此头文件
#include <algorithm>  // 为 std::min, std::max 添加此头文件

// 三维点坐标 (整数索引 x, y, z)
using Point3D = std::tuple<int, int, int>;

// Point3D类型的自定义哈希函数结构体
struct Point3DHash
{
    std::size_t operator()(const Point3D &p) const
    {
        auto hash1 = std::hash<int>{}(std::get<0>(p));
        auto hash2 = std::hash<int>{}(std::get<1>(p));
        auto hash3 = std::hash<int>{}(std::get<2>(p));
        // 使用常见的哈希组合方法将多个哈希值合并为一个
        std::size_t seed = 0;
        seed ^= hash1 + 0x9e3779b9 + (seed << 6) + (seed >> 2);
        seed ^= hash2 + 0x9e3779b9 + (seed << 6) + (seed >> 2);
        seed ^= hash3 + 0x9e3779b9 + (seed << 6) + (seed >> 2);
        return seed;
    }
};

// 可以在此处添加项目中其他需要共享的类型定义

// 表示时间区间的结构体
struct TimeInterval
{
    long long start_time_;
    long long end_time_;
    std::string flight_id_;

    TimeInterval(long long start, long long end, const std::string &id)
        : start_time_(start), end_time_(end), flight_id_(id) {}

    // 检查当前时间区间是否与另一个时间区间重叠
    bool overlaps(const TimeInterval &other) const
    {
        // 判断重叠的逻辑：
        // 1. 如果一个区间的开始时间在另一个区间的结束时间之后，或者一个区间的结束时间在另一个区间的开始时间之前，则它们不重叠
        // 2. 用公式表示：!(other.end_time_ < this->start_time_ || this->end_time_ < other.start_time_)
        // 3. 更直观的重叠判断条件：max(start1, start2) < min(end1, end2)
        // 注意事项：
        // - 区间采用闭区间[start, end]表示，即包含端点
        // - 当一个区间的结束时间等于另一个区间的开始时间时，默认不算重叠
        // - 如果需要将相邻时刻也算作重叠，则需要使用 <= 和 >= 运算符
        return std::max(this->start_time_, other.start_time_) <= std::min(this->end_time_, other.end_time_);
    }

    // 检查当前时间区间是否可以与另一个时间区间合并（要求属于同一个飞行编号且时间重叠）
    bool can_merge_with(const TimeInterval &other) const
    {
        return this->flight_id_ == other.flight_id_ && this->overlaps(other);
    }

    // 合并两个时间区间（调用此函数前需确保两个区间可以合并）
    TimeInterval merged_with(const TimeInterval &other) const
    {
        // 注意：调用者需要确保区间可以合并
        // 也可以在此处添加检查：
        // if (!can_merge_with(other)) {
        //     throw std::logic_error("无法合并不满足合并条件的区间。");
        // }
        return TimeInterval(
            std::min(this->start_time_, other.start_time_),
            std::max(this->end_time_, other.end_time_),
            this->flight_id_);
    }

    // 定义TimeInterval的小于运算符，用于std::sort排序或其他需要比较TimeInterval的场景
    bool operator<(const TimeInterval &other) const
    {
        if (start_time_ != other.start_time_)
        {
            return start_time_ < other.start_time_;
        }
        if (end_time_ != other.end_time_)
        {
            return end_time_ < other.end_time_;
        }
        return flight_id_ < other.flight_id_;
    }
};
