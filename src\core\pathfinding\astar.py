from heapq import heappush, heappop
import math
from ..node_3d import GridNode3D
from typing import List, Set, Dict, Tuple, Optional
from ...utils.logging import get_logger

logger = get_logger(__name__)


def manhattan_distance_3d(y1: int, x1: int, z1: int, y2: int, x2: int, z2: int) -> int:
    """计算3D空间中的曼哈顿距离"""
    return abs(y2 - y1) + abs(x2 - x1) + abs(z2 - z1)


def euclidean_distance_3d(
    y1: int, x1: int, z1: int, y2: int, x2: int, z2: int
) -> float:
    """计算3D空间中的欧几里得距离"""
    return math.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2 + (z2 - z1) ** 2)


class AStar3D:
    def __init__(self, map3d):
        """初始化A*算法"""
        self.map = map3d
        self.open: List[Tuple[float, int, GridNode3D]] = []
        self.closed: Set[Tuple[int, int, int, int]] = set()
        self.nodes: Dict[Tuple[int, int, int, int], GridNode3D] = {}
        self.constraints: Set[Tuple] = set()
        self.agent_id: Optional[str] = None
        self.start: Optional[Tuple[int, int, int]] = None
        self.goal: Optional[Tuple[int, int, int]] = None

    def reset(self):
        """重置所有结构"""
        self.open = []
        self.closed = set()
        self.nodes = {}

    def _check_constraints_and_collisions(
        self, pos, t, agent_id, constraints, occupancy_map
    ):
        """检查位置和时间是否满足约束条件和碰撞检测

        Args:
            pos: 位置坐标 (y, x, z)
            t: 时间戳
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象

        Returns:
            Tuple[bool, str]: (是否可行, 失败原因)，如果可行返回(True, None)
        """
        # 首先检查地图中的静态障碍物类型
        if not self.map.traversable_map[pos[0], pos[1], pos[2]]:
            obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
            return False, f"位置 {pos} 与障碍物类型 {obstacle_types} 发生碰撞"

        # 检查与其他无人机的碰撞
        if occupancy_map:
            has_collision, colliding_agent = occupancy_map.check_collision(pos, t)
            if has_collision and colliding_agent != agent_id:
                return False, f"与无人机 {colliding_agent} 在时刻 {t} 发生碰撞"

        # 检查约束条件
        if constraints:
            for constraint in constraints:
                c_type, c_data = constraint
                if c_type == "v":  # 顶点约束
                    c_agent, c_y, c_x, c_z, c_t = c_data
                    if (
                        agent_id == c_agent
                        and pos[0] == c_y
                        and pos[1] == c_x
                        and pos[2] == c_z
                        and t == c_t
                    ):
                        return False, f"违反约束 {constraint}"

        return True, None

    def get_neighbors(self, node: GridNode3D, min_height: int) -> List[GridNode3D]:
        """获取当前节点的有效后继节点"""
        successors = []
        neighbors = self.map.get_neighbors(node.y, node.x, node.z)

        for ny, nx, nz in neighbors:
            # 检查最小巡航高度限制
            if nz < min_height:
                continue

            # 创建基本的后继节点，不计算评估值
            successor = GridNode3D(ny, nx, nz, node.t + 1)
            successors.append(successor)

        return successors

    def _vertical_takeoff(
        self,
        start: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,  # 主要是针对多无人机同时起飞情况
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """垂直起飞阶段"""
        path = []
        current_t = start_time if start_time is not None else 0

        # 从起点垂直上升到最小巡航高度
        for z in range(start[2], min_height + 1):
            pos = (start[0], start[1], z)

            # 检查约束和碰撞
            is_valid, error = self._check_constraints_and_collisions(
                pos, current_t, agent_id, constraints, occupancy_map
            )
            if not is_valid:
                return None, f"垂直爬升阶段在高度 {z} {error}"

            node = GridNode3D(start[0], start[1], z, current_t)
            path.append(node)
            current_t += 1

        return path, None

    def _vertical_landing(
        self,
        last_cruise_node: GridNode3D,
        goal: Tuple[int, int, int],
        agent_id: str,
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """垂直降落阶段"""
        path = []
        current_t = last_cruise_node.t + 1

        # 从巡航高度垂直下降到目标点
        for z in range(last_cruise_node.z - 1, goal[2] - 1, -1):
            pos = (goal[0], goal[1], z)

            # 检查约束和碰撞
            is_valid, error = self._check_constraints_and_collisions(
                pos, current_t, agent_id, constraints, occupancy_map
            )
            if not is_valid:
                return None, f"降落阶段在高度 {z} {error}"

            node = GridNode3D(goal[0], goal[1], z, current_t)
            path.append(node)
            current_t += 1

        return path, None

    def _find_cruise_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        start_time: Optional[int],
        occupancy_map,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """巡航阶段的路径规划"""
        # 首先检查起点和终点
        for pos, desc in [(start, "起点"), (goal, "终点")]:
            if not self.map.traversable_map[pos[0], pos[1], pos[2]]:
                obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
                return None, f"{desc} {pos} 与障碍物类型 {obstacle_types} 发生碰撞"

        self.reset()
        self.agent_id = str(agent_id)
        self.start = start
        self.goal = goal
        self.constraints = set(constraints) if constraints else set()

        # 设置初始时间
        initial_t = start_time if start_time is not None else 0

        # 创建起始节点
        start_node = GridNode3D(start[0], start[1], start[2], initial_t)
        start_node.g = 0
        start_node.h = manhattan_distance_3d(
            start[0], start[1], start[2], goal[0], goal[1], goal[2]
        )
        start_node.f = start_node.h

        # 添加到open列表
        heappush(self.open, (start_node.f, 0, start_node))
        self.nodes[(start_node.y, start_node.x, start_node.z, start_node.t)] = (
            start_node
        )

        while self.open:
            current = heappop(self.open)[2]

            # 如果到达目标
            if current.y == goal[0] and current.x == goal[1] and current.z == goal[2]:
                return self._extract_path(current), None

            # 将当前节点添加到closed列表
            self.closed.add((current.y, current.x, current.z, current.t))

            # 获取后继节点
            successors = self.get_neighbors(current, min_height)

            for successor in successors:
                # 如果节点已经在closed列表中，跳过
                if (successor.y, successor.x, successor.z, successor.t) in self.closed:
                    continue

                # 检查约束和碰撞
                is_valid, error = self._check_constraints_and_collisions(
                    (successor.y, successor.x, successor.z),
                    successor.t,
                    agent_id,
                    constraints,
                    occupancy_map,
                )
                if not is_valid:
                    continue

                # 计算节点的评估值
                successor.g = current.g + 1
                successor.h = manhattan_distance_3d(
                    successor.y, successor.x, successor.z, goal[0], goal[1], goal[2]
                )
                successor.f = successor.g + successor.h
                successor.parent = current

                # 检查是否需要更新现有节点
                existing = self.nodes.get(
                    (successor.y, successor.x, successor.z, successor.t)
                )
                if not existing or successor.g < existing.g:
                    self.nodes[(successor.y, successor.x, successor.z, successor.t)] = (
                        successor
                    )
                    heappush(self.open, (successor.f, len(self.open), successor))

        return None, "巡航阶段无法找到可行路径"

    def _extract_path(self, node: GridNode3D) -> List[GridNode3D]:
        """从目标节点回溯提取完整路径"""
        path = []
        current = node
        while current:
            path.append(current)
            current = current.parent
        return path[::-1]  # 反转路径

    def find_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """
        使用A*算法寻找路径，包含垂直起飞和降落阶段
        :param start: 起点坐标 (y, x, z)
        :param goal: 终点坐标 (y, x, z)
        :param min_height: 最小巡航高度
        :param agent_id: 智能体ID
        :param constraints: CBS约束列表
        :param start_time: 开始时间戳
        :param occupancy_map: 占用图对象
        :return: (路径点列表, 失败原因) 如果成功返回(path, None)，失败返回(None, 失败原因)
        """
        # 1. 垂直起飞阶段
        takeoff_path, error = self._vertical_takeoff(
            start, min_height, agent_id, start_time, occupancy_map, constraints
        )
        if not takeoff_path:
            return None, error, None

        # 2. 巡航阶段
        cruise_start = takeoff_path[-1]
        cruise_goal = (goal[0], goal[1], min_height)  # 在巡航高度的目标点
        cruise_start_time = cruise_start.t if cruise_start else start_time
        cruise_path, error = self._find_cruise_path(
            (cruise_start.y, cruise_start.x, cruise_start.z),
            cruise_goal,
            min_height,
            agent_id,
            constraints,
            cruise_start_time,
            occupancy_map,
        )
        if not cruise_path:
            return None, error, None

        # 3. 垂直降落阶段
        landing_path, error = self._vertical_landing(
            cruise_path[-1], goal, agent_id, occupancy_map, constraints
        )
        if not landing_path:
            return None, error

        # 合并完整路径
        complete_path = takeoff_path[:-1] + cruise_path + landing_path
        return complete_path, None
