from typing import Dict, List, Optional, Tuple
from ...utils.logging import get_logger
from .base import MessageHandler

logger = get_logger(__name__)


class StateHandler(MessageHandler):
    """处理状态变更的消息处理器"""

    def handle_message(self, message: Dict) -> Tuple[Optional[List], Optional[str]]:
        """
        处理状态变更消息

        Args:
            message: 消息字典，包含状态变更信息

        Returns:
            Tuple[Optional[List], Optional[str]]: (None, None) 因为状态变更不需要返回路径
        """
        try:
            flight_id = message.get("id")
            desc = message.get("desc")
            # source = message.get("source")

            if not flight_id or not desc:
                return None, "缺少必要的消息字段"

            # 处理需要删除路径的状态
            if desc in ["审批驳回", "结束-任务取消", "结束-执行异常"]:
                # 从OccupancyMap中删除agent
                self.occupancy_map.remove_agent(flight_id)

                # 从planned_paths中删除flight_id对应的路径
                if flight_id in self.planned_paths:
                    del self.planned_paths[flight_id]
                    logger.info(
                        f"已删除内存中planned_paths中航班 {flight_id} 的路径信息"
                    )

            # 处理需要更新状态的情况
            elif desc in ["审批通过", "待执行", "执行中"]:
                # 更新planned_paths中对应flight_id的source状态
                if flight_id in self.planned_paths:
                    self.planned_paths[flight_id]["source"] = desc
                    logger.info(
                        f"已更新内存中planned_paths中航班 {flight_id} 的状态为 {desc}"
                    )

            return None, None

        except Exception as e:
            error_msg = f"处理状态变更消息时出错: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
