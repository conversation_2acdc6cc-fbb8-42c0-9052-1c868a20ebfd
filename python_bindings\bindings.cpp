/**
 * @file bindings.cpp
 * @brief Main pybind11 bindings for Multi-Agent Pathfinding C++ implementation
 *
 * 为 C++ JPS 算法提供 Python 绑定接口
 */

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <pybind11/operators.h>

// 引入 C++ 项目头文件
#include "JPS.h"
#include "Map3D.h"
#include "OccupancyMap.h"
#include "GridNode3D.h"
#include "GridConverter.h"
#include "ObstacleTypeManager.h"
#include "common_types.h"

// 引入类型转换辅助
#include "type_converters.hpp"

namespace py = pybind11;
using namespace type_converters;

PYBIND11_MODULE(pathfinding_cpp, m)
{
    m.doc() = "Multi-Agent Pathfinding with JPS algorithm - C++ Python bindings";

    // ==================== GridNode3D 类绑定 ====================
    py::class_<GridNode3D>(m, "GridNode3D", "3D Grid Node for pathfinding")
        .def(py::init<float, float, float, long long, float, float, GridNode3D *, int, bool>(),
             "Constructor",
             py::arg("x"), py::arg("y"), py::arg("z"),
             py::arg("t") = 0LL,
             py::arg("g") = std::numeric_limits<float>::infinity(),
             py::arg("h") = 0.0f,
             py::arg("parent") = nullptr,
             py::arg("jump_step") = 5,
             py::arg("need_sight") = true)
        .def_readwrite("x", &GridNode3D::x, "X coordinate")
        .def_readwrite("y", &GridNode3D::y, "Y coordinate")
        .def_readwrite("z", &GridNode3D::z, "Z coordinate")
        .def_readwrite("t", &GridNode3D::t, "Timestamp")
        .def_readwrite("g", &GridNode3D::g, "G cost (actual cost from start)")
        .def_readwrite("h", &GridNode3D::h, "H cost (heuristic cost to goal)")
        .def_readwrite("f", &GridNode3D::f, "F cost (total cost)")
        .def_readwrite("jump_step", &GridNode3D::jump_step, "Jump step size")
        .def_readwrite("need_sight", &GridNode3D::need_sight, "Need sight check")
        .def("update_f_cost", &GridNode3D::update_f_cost, "Update F cost")
        .def("__repr__", [](const GridNode3D &node)
             { return "<GridNode3D(x=" + std::to_string(node.x) +
                      ", y=" + std::to_string(node.y) +
                      ", z=" + std::to_string(node.z) +
                      ", t=" + std::to_string(node.t) + ")>"; });

    // ==================== ObstacleTypeInfo 结构体绑定 ====================
    py::class_<ObstacleTypeInfo>(m, "ObstacleTypeInfo", "Obstacle type information")
        .def(py::init<>(), "Default constructor")
        .def_readwrite("description", &ObstacleTypeInfo::description, "Obstacle type description")
        .def_readwrite("created_at", &ObstacleTypeInfo::created_at, "Creation timestamp")
        .def("__repr__", [](const ObstacleTypeInfo &info)
             { return "<ObstacleTypeInfo(description='" + info.description +
                      "', created_at='" + info.created_at + "')>"; });

    // ==================== ObstacleTypeManager 类绑定 ====================
    py::class_<ObstacleTypeManager>(m, "ObstacleTypeManager", "Obstacle type manager")
        .def(py::init<int, int, int>(), "Constructor",
             py::arg("height"), py::arg("width"), py::arg("depth"))
        .def("add_obstacle_type", &ObstacleTypeManager::add_obstacle_type,
             "Add new obstacle type",
             py::arg("name"), py::arg("description"), py::arg("created_at_str"))
        .def("remove_obstacle_type", &ObstacleTypeManager::remove_obstacle_type,
             "Remove obstacle type and all its positions",
             py::arg("name"))
        .def("add_obstacle_positions", [](ObstacleTypeManager &self, const std::string &type_name, const py::list &positions_list)
             {
                 std::set<Point3D> positions_set;
                 for (auto item : positions_list) {
                     py::tuple pos_tuple = item.cast<py::tuple>();
                     Point3D point = tuple_to_point3d(pos_tuple);
                     positions_set.insert(point);
                 }
                 self.add_obstacle_positions(type_name, positions_set); }, "Add obstacle positions for specified type", py::arg("type_name"), py::arg("positions"))
        .def("remove_obstacle_positions", [](ObstacleTypeManager &self, const std::string &type_name, const py::list &positions_list)
             {
                 std::set<Point3D> positions_set;
                 for (auto item : positions_list) {
                     py::tuple pos_tuple = item.cast<py::tuple>();
                     Point3D point = tuple_to_point3d(pos_tuple);
                     positions_set.insert(point);
                 }
                 self.remove_obstacle_positions(type_name, positions_set); }, "Remove obstacle positions for specified type", py::arg("type_name"), py::arg("positions"))
        .def("get_positions_for_type", [](const ObstacleTypeManager &self, const std::string &type_name)
             {
                 auto positions = self.get_positions_for_type(type_name);
                 py::list result;
                 for (const auto& pos : positions) {
                     result.append(point3d_to_tuple(pos));
                 }
                 return result; }, "Get all positions for specified obstacle type", py::arg("type_name"))
        .def("get_type_info", &ObstacleTypeManager::get_type_info, "Get information for specified obstacle type", py::arg("type_name"))
        .def("is_obstacle", [](const ObstacleTypeManager &self, const py::tuple &pos_tuple)
             {
                 Point3D point = tuple_to_point3d(pos_tuple);
                 return self.is_obstacle(point); }, "Check if position is an obstacle", py::arg("position"))
        .def("is_obstacle_of_type", [](const ObstacleTypeManager &self, const std::string &type_name, const py::tuple &pos_tuple)
             {
                 Point3D point = tuple_to_point3d(pos_tuple);
                 return self.is_obstacle_of_type(type_name, point); }, "Check if position is an obstacle of specified type", py::arg("type_name"), py::arg("position"))
        .def("get_all_type_names", &ObstacleTypeManager::get_all_type_names, "Get all defined obstacle type names");

    // ==================== GridConverter 类绑定 ====================
    py::class_<GridCellSize>(m, "GridCellSize", "Grid cell size configuration")
        .def(py::init([](double lat_resolution, double lon_resolution, double alt_resolution)
                      { return GridCellSize{lat_resolution, lon_resolution, alt_resolution}; }),
             py::arg("lat_resolution"), py::arg("lon_resolution"), py::arg("alt_resolution"))
        .def_readwrite("lat_resolution", &GridCellSize::lat_resolution)
        .def_readwrite("lon_resolution", &GridCellSize::lon_resolution)
        .def_readwrite("alt_resolution", &GridCellSize::alt_resolution);

    py::class_<GeoCoordinate>(m, "GeoCoordinate", "Geographic coordinate")
        .def(py::init([](double lat, double lon, double alt)
                      { return GeoCoordinate{lat, lon, alt}; }),
             py::arg("lat"), py::arg("lon"), py::arg("alt"))
        .def_readwrite("lat", &GeoCoordinate::lat)
        .def_readwrite("lon", &GeoCoordinate::lon)
        .def_readwrite("alt", &GeoCoordinate::alt);

    py::class_<GridConverter>(m, "GridConverter", "Geographic to grid coordinate converter")
        .def(py::init<const GridCellSize &, const GeoCoordinate &>(),
             py::arg("cell_size"), py::arg("min_coords"))
        .def("geographic_to_grid", [](const GridConverter &self, double lat, double lon, double alt)
             {
                 GeoCoordinate geo_coord{lat, lon, alt};
                 auto result = self.geographic_to_grid(geo_coord);
                 return py::make_tuple(std::get<0>(result), std::get<1>(result), std::get<2>(result)); }, "Convert geographic coordinates to grid coordinates", py::arg("lat"), py::arg("lon"), py::arg("alt"))
        .def("grid_to_geographic", [](const GridConverter &self, float grid_x, float grid_y, float grid_z)
             {
                 auto result = self.grid_to_geographic(grid_x, grid_y, grid_z);
                 return py::make_tuple(result.lat, result.lon, result.alt); }, "Convert grid coordinates to geographic coordinates", py::arg("grid_x"), py::arg("grid_y"), py::arg("grid_z"));

    // ==================== OccupancyMap 类绑定 ====================
    py::class_<OccupancyMap>(m, "OccupancyMap", "Occupancy map for collision detection")
        .def(py::init<int, int, int, int>(),
             "Constructor",
             py::arg("height"), py::arg("width"), py::arg("depth"), py::arg("time_buffer"))
        .def("add_path", [](OccupancyMap &self, const std::string &flight_id, const py::list &path_list)
             {
                 std::vector<GridNode3D> path = list_to_gridnode3d_vector(path_list);
                 self.add_path(flight_id, path); }, "Add a flight path to occupancy map", py::arg("flight_id"), py::arg("path"))
        .def("check_collision", [](const OccupancyMap &self, const py::tuple &point_tuple, long long timestamp)
             {
                 Point3D point = tuple_to_point3d(point_tuple);
                 return self.check_collision(point, timestamp); }, "Check if a point is occupied at given timestamp", py::arg("point"), py::arg("timestamp"))
        .def("remove_agent", &OccupancyMap::remove_agent, "Remove all occupancy data for a flight", py::arg("flight_id"))
        .def("get_agent_occupied_points", [](const OccupancyMap &self, const std::string &flight_id)
             {
                 auto points = self.get_agent_occupied_points(flight_id);
                 py::list result;
                 for (const auto& point : points) {
                     result.append(point3d_to_tuple(point));
                 }
                 return result; }, "Get all points occupied by a flight", py::arg("flight_id"));

    // ==================== Map3D 类绑定 ====================
    py::class_<Map3D>(m, "Map3D", "3D map for pathfinding")
        .def(py::init<int, int, int, const GridConverter &>(),
             "Constructor",
             py::arg("height"), py::arg("width"), py::arg("depth"), py::arg("converter"))
        .def("is_traversable", [](const Map3D &self, const py::tuple &point_tuple)
             {
                 Point3D point = tuple_to_point3d(point_tuple);
                 return self.is_traversable(point); }, "Check if a point is traversable", py::arg("point"))
        .def("is_traversable", [](const Map3D &self, int x, int y, int z)
             { return self.is_traversable(x, y, z); }, "Check if coordinates are traversable", py::arg("x"), py::arg("y"), py::arg("z"))
        .def("get_height", &Map3D::get_height, "Get map height")
        .def("get_width", &Map3D::get_width, "Get map width")
        .def("get_depth", &Map3D::get_depth, "Get map depth")
        .def("get_obstacle_manager", static_cast<ObstacleTypeManager &(Map3D::*)()>(&Map3D::get_obstacle_manager), "Get obstacle manager reference", py::return_value_policy::reference_internal)
        .def("get_converter", &Map3D::get_converter, "Get grid converter reference", py::return_value_policy::reference_internal)
        .def("update_internal_obstacles", &Map3D::update_internal_obstacles, "Update internal obstacle representation")

        // 添加基于地理坐标的圆柱形禁飞区
        .def("add_solid_cylindrical_no_fly_zone", [](Map3D &self, double center_lat_deg, double center_lon_deg, double radius_meters, const std::string &zone_name, int buffer_distance_meters, const py::dict &planned_paths_dict, int boundary_thickness_grids)
             {

                 // 转换 Python 字典到 C++ map
                 std::map<std::string, std::vector<GridNode3D>> planned_paths_map;
                 for (auto item : planned_paths_dict) {
                     std::string flight_id = item.first.cast<std::string>();
                     py::list path_list = item.second.cast<py::list>();
                     std::vector<GridNode3D> path = list_to_gridnode3d_vector(path_list);
                     planned_paths_map[flight_id] = path;
                 }

                 std::vector<std::string> conflicting_flight_ids;
                 bool success = self.add_solid_cylindrical_no_fly_zone(
                     center_lat_deg, center_lon_deg, radius_meters, zone_name,
                     buffer_distance_meters, planned_paths_map, boundary_thickness_grids,
                     conflicting_flight_ids);

                 return py::make_tuple(success, conflicting_flight_ids); }, "Add solid cylindrical no-fly zone using geographic coordinates", py::arg("center_lat_deg"), py::arg("center_lon_deg"), py::arg("radius_meters"), py::arg("zone_name"), py::arg("buffer_distance_meters") = 0, py::arg("planned_paths_dict") = py::dict(), py::arg("boundary_thickness_grids") = 1)

        // 添加基于栅格坐标的圆柱形禁飞区
        .def("add_solid_cylindrical_no_fly_zone_grid", [](Map3D &self, float center_x_grid, float center_y_grid, float radius_in_grids, const std::string &zone_name, const py::dict &planned_paths_dict, int boundary_thickness_grids)
             {

                 // 转换 Python 字典到 C++ map
                 std::map<std::string, std::vector<GridNode3D>> planned_paths_map;
                 for (auto item : planned_paths_dict) {
                     std::string flight_id = item.first.cast<std::string>();
                     py::list path_list = item.second.cast<py::list>();
                     std::vector<GridNode3D> path = list_to_gridnode3d_vector(path_list);
                     planned_paths_map[flight_id] = path;
                 }

                 std::vector<std::string> conflicting_flight_ids;
                 bool success = self.add_solid_cylindrical_no_fly_zone_grid(
                     center_x_grid, center_y_grid, radius_in_grids, zone_name,
                     planned_paths_map, boundary_thickness_grids, conflicting_flight_ids);

                 return py::make_tuple(success, conflicting_flight_ids); }, "Add solid cylindrical no-fly zone using grid coordinates", py::arg("center_x_grid"), py::arg("center_y_grid"), py::arg("radius_in_grids"), py::arg("zone_name"), py::arg("planned_paths_dict") = py::dict(), py::arg("boundary_thickness_grids") = 1)

        // 添加空心多边形禁飞区
        .def("add_hollow_polygonal_no_fly_zone", [](Map3D &self, const py::list &polygon_vertices_list, const std::string &zone_name, double offset_meters_b, int boundary_thickness_grids, const py::dict &planned_paths_dict)
             {

                 // 转换 Python 顶点列表到 C++ vector
                 std::vector<GeoCoordinate> polygon_vertices_geo;
                 for (auto vertex : polygon_vertices_list) {
                     py::tuple vertex_tuple = vertex.cast<py::tuple>();
                     if (vertex_tuple.size() != 3) {
                         throw std::invalid_argument("Each vertex must be a tuple of (lat, lon, alt)");
                     }
                     double lat = vertex_tuple[0].cast<double>();
                     double lon = vertex_tuple[1].cast<double>();
                     double alt = vertex_tuple[2].cast<double>();
                     polygon_vertices_geo.push_back({lat, lon, alt});
                 }

                 // 转换 Python 字典到 C++ map
                 std::map<std::string, std::vector<GridNode3D>> planned_paths_map;
                 for (auto item : planned_paths_dict) {
                     std::string flight_id = item.first.cast<std::string>();
                     py::list path_list = item.second.cast<py::list>();
                     std::vector<GridNode3D> path = list_to_gridnode3d_vector(path_list);
                     planned_paths_map[flight_id] = path;
                 }

                 std::vector<std::string> conflicting_flight_ids;
                 bool success = self.add_hollow_polygonal_no_fly_zone(
                     polygon_vertices_geo, zone_name, offset_meters_b,
                     boundary_thickness_grids, planned_paths_map, conflicting_flight_ids);

                 return py::make_tuple(success, conflicting_flight_ids); }, "Add hollow polygonal no-fly zone using geographic coordinates", py::arg("polygon_vertices"), py::arg("zone_name"), py::arg("offset_meters") = 0.0, py::arg("boundary_thickness_grids") = 1, py::arg("planned_paths_dict") = py::dict())

        // 添加基于栅格坐标的空心多边形禁飞区
        .def("add_hollow_polygonal_no_fly_zone_grid", [](Map3D &self, const py::list &polygon_vertices_list, const std::string &zone_name, float offset_grids, int boundary_thickness_grids, const py::dict &planned_paths_dict)
             {

                 // 转换 Python 顶点列表到 C++ vector (只使用 x, y 坐标)
                 std::vector<std::pair<float, float>> polygon_vertices_grid;
                 for (auto vertex : polygon_vertices_list) {
                     py::tuple vertex_tuple = vertex.cast<py::tuple>();
                     if (vertex_tuple.size() < 2) {
                         throw std::invalid_argument("Each vertex must be a tuple of at least (x, y) in grid coordinates");
                     }
                     float x = vertex_tuple[0].cast<float>();
                     float y = vertex_tuple[1].cast<float>();
                     polygon_vertices_grid.push_back(std::make_pair(x, y));
                 }

                 // 转换 Python 字典到 C++ map
                 std::map<std::string, std::vector<GridNode3D>> planned_paths_map;
                 for (auto item : planned_paths_dict) {
                     std::string flight_id = item.first.cast<std::string>();
                     py::list path_list = item.second.cast<py::list>();
                     std::vector<GridNode3D> path = list_to_gridnode3d_vector(path_list);
                     planned_paths_map[flight_id] = path;
                 }

                 std::vector<std::string> conflicting_flight_ids;
                 bool success = self.add_hollow_polygonal_no_fly_zone_grid(
                     polygon_vertices_grid, zone_name, offset_grids,
                     boundary_thickness_grids, planned_paths_map, conflicting_flight_ids);

                 return py::make_tuple(success, conflicting_flight_ids); }, "Add hollow polygonal no-fly zone using grid coordinates", py::arg("polygon_vertices"), py::arg("zone_name"), py::arg("offset_grids") = 0.0f, py::arg("boundary_thickness_grids") = 1, py::arg("planned_paths_dict") = py::dict())

        // 删除禁飞区
        .def("remove_no_fly_zone", &Map3D::remove_no_fly_zone, "Remove a no-fly zone by name", py::arg("zone_name"));

    // ==================== JPS 类绑定 ====================
    py::class_<JPS>(m, "JPS", "Jump Point Search pathfinding algorithm")
        .def(py::init<Map3D *, OccupancyMap *, double, double, double, int, bool, double, double, int>(), "Constructor", py::arg("map_data"), py::arg("occupancy_map_data"), py::arg("takeoff_speed"), py::arg("cruise_speed"), py::arg("landing_speed"), py::arg("max_steps"), py::arg("need_smooth"), py::arg("smoothness"), py::arg("heuristic_weight") = 2.0, py::arg("jump_step_size") = 5)
        .def("find_path", [](JPS &self, const py::tuple &start_tuple, const py::tuple &goal_tuple, int min_height, const std::string &agent_id, long long start_time)
             {

                 // 验证参数
                 validate_find_path_params(start_tuple, goal_tuple, min_height, agent_id, start_time);

                 // 转换参数类型
                 GridNode3D start_node = tuple_to_gridnode3d(start_tuple);
                 GridNode3D goal_node = tuple_to_gridnode3d(goal_tuple);

                 // 调用 C++ 方法
                 auto [complete_path, turn_path, error_msg] = self.find_path(
                     start_node, goal_node, min_height, agent_id, start_time);

                 // 转换返回结果为 Python 字典
                 return create_path_result(complete_path, turn_path, error_msg); }, "Find path from start to goal", py::arg("start"), py::arg("goal"), py::arg("min_height"), py::arg("agent_id"), py::arg("start_time"));

    // ==================== 辅助函数 ====================
    m.def("create_grid_converter", [](double lat_size, double lon_size, double alt_size, double min_lat, double min_lon, double min_alt)
          {
              GridCellSize cell_size{lat_size, lon_size, alt_size};
              GeoCoordinate min_coords{min_lat, min_lon, min_alt};
              return GridConverter(cell_size, min_coords); }, "Create a GridConverter instance", py::arg("lat_size"), py::arg("lon_size"), py::arg("alt_size"), py::arg("min_lat"), py::arg("min_lon"), py::arg("min_alt"));

    // ==================== 版本信息 ====================
    m.attr("__version__") = "0.1.0";
}
